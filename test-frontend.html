<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .image-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        .image-item {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
        .image-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s;
        }
        .image-item:hover .image-actions {
            opacity: 1;
        }
        .btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .btn-replace {
            background: #007bff;
            color: white;
        }
        .btn-remove {
            background: #dc3545;
            color: white;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.2s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.disabled {
            border-color: #ccc;
            background: #f5f5f5;
            cursor: not-allowed;
        }
        .image-counter {
            margin-bottom: 10px;
            font-size: 14px;
            color: #666;
        }
        .image-type {
            position: absolute;
            top: 8px;
            left: 8px;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            color: white;
        }
        .type-existing {
            background: #28a745;
        }
        .type-new {
            background: #007bff;
        }
    </style>
</head>
<body>
    <h1>Test Image Upload & Replace</h1>
    
    <div class="image-counter">
        <span id="imageCount">0</span>/5 ảnh
    </div>
    
    <div class="upload-area" id="uploadArea">
        <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
        <input type="file" id="replaceInput" accept="image/*" style="display: none;">
        <p>Nhấp để chọn ảnh hoặc kéo thả ảnh vào đây</p>
        <p style="font-size: 12px; color: #666;">PNG, JPG, GIF tối đa 10MB</p>
    </div>
    
    <div class="image-preview" id="imagePreview"></div>

    <script>
        let images = [];
        let replacingIndex = null;
        
        const fileInput = document.getElementById('fileInput');
        const replaceInput = document.getElementById('replaceInput');
        const uploadArea = document.getElementById('uploadArea');
        const imagePreview = document.getElementById('imagePreview');
        const imageCount = document.getElementById('imageCount');
        
        uploadArea.addEventListener('click', () => {
            if (images.length < 5) {
                fileInput.click();
            }
        });
        
        fileInput.addEventListener('change', handleFileSelect);
        replaceInput.addEventListener('change', handleReplaceFile);
        
        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            
            if (files.length + images.length > 5) {
                alert('Tối đa 5 hình ảnh');
                return;
            }
            
            files.forEach(file => {
                if (file.size > 10 * 1024 * 1024) {
                    alert('File quá lớn: ' + file.name);
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    images.push({
                        file: file,
                        url: e.target.result,
                        isNew: true
                    });
                    updateDisplay();
                };
                reader.readAsDataURL(file);
            });
            
            e.target.value = '';
        }
        
        function handleReplaceFile(e) {
            const file = e.target.files[0];
            if (!file || replacingIndex === null) return;
            
            if (file.size > 10 * 1024 * 1024) {
                alert('File quá lớn');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = (e) => {
                images[replacingIndex] = {
                    file: file,
                    url: e.target.result,
                    isNew: true
                };
                updateDisplay();
                alert('Đã thay thế hình ảnh');
            };
            reader.readAsDataURL(file);
            
            replacingIndex = null;
            e.target.value = '';
        }
        
        function replaceImage(index) {
            replacingIndex = index;
            replaceInput.click();
        }
        
        function removeImage(index) {
            images.splice(index, 1);
            updateDisplay();
        }
        
        function updateDisplay() {
            imageCount.textContent = images.length;
            
            // Update upload area
            if (images.length >= 5) {
                uploadArea.classList.add('disabled');
                uploadArea.innerHTML = '<p>Đã đạt giới hạn tối đa 5 ảnh</p>';
            } else {
                uploadArea.classList.remove('disabled');
                uploadArea.innerHTML = `
                    <p>Nhấp để chọn ảnh hoặc kéo thả ảnh vào đây</p>
                    <p style="font-size: 12px; color: #666;">PNG, JPG, GIF tối đa 10MB</p>
                `;
            }
            
            // Update image preview
            imagePreview.innerHTML = '';
            images.forEach((image, index) => {
                const div = document.createElement('div');
                div.className = 'image-item';
                div.innerHTML = `
                    <img src="${image.url}" alt="Preview ${index + 1}">
                    <div class="image-type ${image.isNew ? 'type-new' : 'type-existing'}">
                        ${image.isNew ? 'Mới' : 'Có sẵn'}
                    </div>
                    <div class="image-actions">
                        <button class="btn btn-replace" onclick="replaceImage(${index})">Thay thế</button>
                        <button class="btn btn-remove" onclick="removeImage(${index})">Xóa</button>
                    </div>
                `;
                imagePreview.appendChild(div);
            });
        }
        
        // Test with some existing images
        const existingImages = [
            'http://localhost:5001/uploads/images-1747917385255.jpg',
            'http://localhost:5001/uploads/images-1747917385280.jpg'
        ];
        
        existingImages.forEach(url => {
            images.push({
                file: null,
                url: url,
                isNew: false
            });
        });
        
        updateDisplay();
    </script>
</body>
</html>
