import React from 'react';

const SimpleImageTest: React.FC = () => {
  const testUrl = 'http://localhost:5001/uploads/683eb72fd25e6013197bc878/images-1747917385255.jpg';

  return (
    <div className="p-4 border rounded">
      <h3 className="font-bold mb-2">Simple Image Test</h3>
      <p className="text-sm mb-2">URL: {testUrl}</p>
      <img 
        src={testUrl}
        alt="Test"
        className="w-32 h-32 object-cover border"
        onLoad={() => console.log('✅ Simple test image loaded')}
        onError={(e) => console.error('❌ Simple test image failed:', e)}
      />
    </div>
  );
};

export default SimpleImageTest;
