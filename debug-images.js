const fs = require('fs');
const path = require('path');
const { MongoClient } = require('mongodb');

// MongoDB URI
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://keydyweb:<EMAIL>/keydyweb?retryWrites=true&w=majority';

async function debugImages() {
  let client;
  
  try {
    console.log('🔍 DEBUGGING IMAGE ISSUES...\n');

    // 1. Check files in uploads directory
    console.log('📁 1. CHECKING FILES IN UPLOADS DIRECTORY:');
    const uploadsDir = path.join(__dirname, 'backend/uploads');
    
    if (fs.existsSync(uploadsDir)) {
      const files = fs.readdirSync(uploadsDir).filter(file =>
        file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.gif') || file.endsWith('.jpeg')
      );
      console.log(`   Found ${files.length} image files:`);
      files.forEach((file, index) => {
        const filePath = path.join(uploadsDir, file);
        const stats = fs.statSync(filePath);
        console.log(`   ${index + 1}. ${file} (${(stats.size / 1024).toFixed(1)}KB)`);
      });
    } else {
      console.log('   ❌ Uploads directory does not exist!');
      return;
    }

    // 2. Check database campaigns
    console.log('\n📊 2. CHECKING DATABASE CAMPAIGNS:');
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    console.log('   ✅ Connected to MongoDB');

    const db = client.db();
    const campaigns = await db.collection('campaigns').find({}).toArray();
    console.log(`   Found ${campaigns.length} campaigns in database:`);

    let problemCount = 0;
    let validCount = 0;

    for (const campaign of campaigns) {
      console.log(`\n   📋 Campaign: ${campaign.title}`);
      console.log(`      ID: ${campaign._id}`);
      
      if (campaign.images && Array.isArray(campaign.images)) {
        console.log(`      Images (${campaign.images.length}):`);
        
        for (const imagePath of campaign.images) {
          console.log(`         - ${imagePath}`);
          
          // Check if file exists
          let filename = '';
          if (imagePath.startsWith('/uploads/')) {
            filename = imagePath.replace('/uploads/', '');
          } else if (imagePath.startsWith('uploads/')) {
            filename = imagePath.replace('uploads/', '');
          } else {
            filename = imagePath;
          }
          
          const fullPath = path.join(uploadsDir, filename);
          if (fs.existsSync(fullPath)) {
            console.log(`           ✅ File exists: ${filename}`);
            validCount++;
          } else {
            console.log(`           ❌ File missing: ${filename}`);
            problemCount++;
            
            // Try to find similar files
            const parts = filename.split('-');
            if (parts.length >= 2) {
              const timestamp = parts[1];
              const similarFiles = fs.readdirSync(uploadsDir).filter(file => 
                file.includes(timestamp) && file.includes(parts[0])
              );
              if (similarFiles.length > 0) {
                console.log(`           🔄 Similar files found: ${similarFiles.join(', ')}`);
              }
            }
          }
        }
      } else {
        console.log(`      No images array found`);
      }
    }

    console.log(`\n📊 3. SUMMARY:`);
    console.log(`   - Total campaigns: ${campaigns.length}`);
    console.log(`   - Valid image references: ${validCount}`);
    console.log(`   - Problem image references: ${problemCount}`);

    // 3. Test URL generation
    console.log(`\n🔗 4. TESTING URL GENERATION:`);
    const testPaths = [
      '/uploads/images-1747917385255.jpg',
      'uploads/images-1747917385255.jpg', 
      'images-1747917385255.jpg',
      'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...',
      'http://localhost:5001/uploads/images-1747917385255.jpg'
    ];

    testPaths.forEach(testPath => {
      const result = getImageUrl(testPath);
      console.log(`   Input:  ${testPath.substring(0, 50)}${testPath.length > 50 ? '...' : ''}`);
      console.log(`   Output: ${result.substring(0, 50)}${result.length > 50 ? '...' : ''}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 Disconnected from MongoDB');
    }
  }
}

// Simulate SafeImage getImageUrl function
function getImageUrl(imagePath) {
  const API_URL = 'http://localhost:5001';
  
  if (!imagePath) return '';
  
  // Handle data URLs (base64 images) - return as is
  if (imagePath.startsWith('data:')) return imagePath;
  
  // Handle blob URLs - return as is
  if (imagePath.startsWith('blob:')) return imagePath;
  
  // Handle full HTTP URLs - return as is
  if (imagePath.startsWith('http')) return imagePath;
  
  // Handle relative paths
  if (imagePath.startsWith('/uploads')) return `${API_URL}${imagePath}`;
  if (imagePath.startsWith('uploads')) return `${API_URL}/${imagePath}`;
  
  // Default case: assume it's a filename only
  return `${API_URL}/uploads/${imagePath}`;
}

// Check if mongodb driver is available
try {
  require('mongodb');
  debugImages();
} catch (error) {
  console.log('❌ MongoDB driver not found. Installing...');
  console.log('Please run: npm install mongodb');
  console.log('Then run this script again.');
}
