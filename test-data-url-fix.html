<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Data URL Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .test-item {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #fafafa;
        }
        .test-item h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }
        .image-container {
            width: 100%;
            height: 120px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .image-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .url-display {
            margin-top: 5px;
            font-size: 10px;
            color: #666;
            word-break: break-all;
            max-height: 40px;
            overflow: hidden;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Data URL Fix</h1>
        <p>Testing image URL processing for different types of URLs</p>
        
        <button onclick="runTests()">Run Tests</button>
        <button onclick="testFileUpload()">Test File Upload</button>
        
        <div class="test-grid" id="testGrid"></div>
        
        <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileSelect(event)">
    </div>

    <script>
        const API_URL = 'http://localhost:5001';
        
        // Simulate the getImageUrl function from SafeImage
        function getImageUrl(imagePath) {
            if (!imagePath) return '';
            
            // Handle data URLs (base64 images) - return as is
            if (imagePath.startsWith('data:')) return imagePath;
            
            // Handle blob URLs - return as is
            if (imagePath.startsWith('blob:')) return imagePath;
            
            // Handle full HTTP URLs - return as is
            if (imagePath.startsWith('http')) return imagePath;
            
            // Handle relative paths
            if (imagePath.startsWith('/uploads')) return `${API_URL}${imagePath}`;
            if (imagePath.startsWith('uploads')) return `${API_URL}/${imagePath}`;
            
            // Default case: assume it's a filename only
            return `${API_URL}/uploads/${imagePath}`;
        }
        
        function createTestItem(name, originalUrl, processedUrl) {
            const testDiv = document.createElement('div');
            testDiv.className = 'test-item';
            
            testDiv.innerHTML = `
                <h4>${name}</h4>
                <div class="image-container">
                    <img src="${processedUrl}" alt="${name}">
                </div>
                <div class="status" id="status-${name.replace(/\s+/g, '-')}">Loading...</div>
                <div class="url-display">
                    <strong>Original:</strong> ${originalUrl.substring(0, 50)}${originalUrl.length > 50 ? '...' : ''}<br>
                    <strong>Processed:</strong> ${processedUrl.substring(0, 50)}${processedUrl.length > 50 ? '...' : ''}
                </div>
            `;
            
            const img = testDiv.querySelector('img');
            const statusDiv = testDiv.querySelector('.status');
            
            img.onload = () => {
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ Loaded successfully';
            };
            
            img.onerror = () => {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Failed to load';
            };
            
            return testDiv;
        }
        
        function runTests() {
            const testGrid = document.getElementById('testGrid');
            testGrid.innerHTML = '';
            
            const testCases = [
                {
                    name: 'Data URL (Base64)',
                    url: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k='
                },
                {
                    name: 'Blob URL',
                    url: 'blob:http://localhost:5173/12345678-1234-1234-1234-123456789abc'
                },
                {
                    name: 'HTTP URL',
                    url: 'http://localhost:5001/uploads/images-1747917385255.jpg'
                },
                {
                    name: 'Relative Path',
                    url: '/uploads/images-1747917385255.jpg'
                },
                {
                    name: 'Filename Only',
                    url: 'images-1747917385255.jpg'
                },
                {
                    name: 'UUID Filename (Should Fail)',
                    url: 'images-1749097293728-56c1ca02-e672-41c8-9607-d11321a870d3.jpg'
                }
            ];
            
            testCases.forEach(testCase => {
                const processedUrl = getImageUrl(testCase.url);
                const testItem = createTestItem(testCase.name, testCase.url, processedUrl);
                testGrid.appendChild(testItem);
            });
        }
        
        function testFileUpload() {
            document.getElementById('fileInput').click();
        }
        
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = (e) => {
                const dataUrl = e.target.result;
                const processedUrl = getImageUrl(dataUrl);
                
                const testGrid = document.getElementById('testGrid');
                const testItem = createTestItem('Uploaded File (Data URL)', dataUrl, processedUrl);
                testGrid.appendChild(testItem);
            };
            reader.readAsDataURL(file);
        }
        
        // Auto-run tests on page load
        window.onload = () => {
            runTests();
        };
    </script>
</body>
</html>
