const mongoose = require('mongoose');
require('dotenv').config({ path: './backend/.env' });

const MONGODB_URI = process.env.MONGODB_URI;

// Campaign schema
const campaignSchema = new mongoose.Schema({
  title: String,
  images: [String],
  image: String
});

const Campaign = mongoose.model('Campaign', campaignSchema);

async function fixDatabaseImages() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get all campaigns
    const campaigns = await Campaign.find({});
    console.log(`📊 Found ${campaigns.length} campaigns`);

    let fixedCount = 0;

    for (const campaign of campaigns) {
      let hasChanges = false;
      const newImages = [];

      console.log(`\n🔍 Checking campaign: ${campaign.title}`);
      console.log(`📁 Current images: ${campaign.images.length}`);

      for (const imagePath of campaign.images) {
        console.log(`  - Checking: ${imagePath}`);
        
        // If image path contains UUID (long filename), remove it
        if (imagePath.includes('-') && imagePath.split('-').length > 2) {
          // Extract timestamp part only
          const parts = imagePath.split('/');
          const filename = parts[parts.length - 1];
          const filenameParts = filename.split('-');
          
          if (filenameParts.length >= 2) {
            const simpleFilename = `${filenameParts[0]}-${filenameParts[1]}.jpg`;
            const newPath = `/uploads/${simpleFilename}`;
            newImages.push(newPath);
            console.log(`    🔄 Fixed: ${imagePath} -> ${newPath}`);
            hasChanges = true;
          } else {
            newImages.push(imagePath);
            console.log(`    ✅ Kept: ${imagePath}`);
          }
        } else {
          newImages.push(imagePath);
          console.log(`    ✅ Kept: ${imagePath}`);
        }
      }

      if (hasChanges) {
        // Update campaign
        await Campaign.findByIdAndUpdate(campaign._id, {
          images: newImages,
          image: newImages.length > 0 ? newImages[0] : ''
        });
        
        fixedCount++;
        console.log(`  ✅ Campaign fixed: ${campaign.title}`);
        console.log(`  📁 Old images: ${campaign.images.length}`);
        console.log(`  📁 New images: ${newImages.length}`);
      } else {
        console.log(`  ✅ Campaign OK: ${campaign.title}`);
      }
    }

    console.log(`\n🎉 Summary:`);
    console.log(`  - Total campaigns: ${campaigns.length}`);
    console.log(`  - Fixed campaigns: ${fixedCount}`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

fixDatabaseImages();
