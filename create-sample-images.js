const fs = require('fs');
const path = require('path');
const https = require('https');

// Create sample images for testing
async function createSampleImages() {
  console.log('🎨 CREATING SAMPLE IMAGES...\n');

  const uploadsDir = path.join(__dirname, 'backend/uploads');
  
  // Create uploads directory if it doesn't exist
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log('📁 Created uploads directory');
  }

  // Check existing files
  const existingFiles = fs.readdirSync(uploadsDir).filter(file =>
    file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.gif') || file.endsWith('.jpeg')
  );

  console.log(`📊 Found ${existingFiles.length} existing image files`);

  if (existingFiles.length >= 5) {
    console.log('✅ Sufficient images already exist, skipping creation');
    return;
  }

  // Sample image URLs (placeholder images)
  const sampleImages = [
    {
      name: 'campaign-1.jpg',
      url: 'https://picsum.photos/800/600?random=1',
      description: 'Campaign sample 1'
    },
    {
      name: 'campaign-2.jpg', 
      url: 'https://picsum.photos/800/600?random=2',
      description: 'Campaign sample 2'
    },
    {
      name: 'campaign-3.jpg',
      url: 'https://picsum.photos/800/600?random=3', 
      description: 'Campaign sample 3'
    },
    {
      name: 'campaign-4.jpg',
      url: 'https://picsum.photos/800/600?random=4',
      description: 'Campaign sample 4'
    },
    {
      name: 'campaign-5.jpg',
      url: 'https://picsum.photos/800/600?random=5',
      description: 'Campaign sample 5'
    }
  ];

  // Create simple colored rectangles as fallback
  const createColoredImage = (filename, color) => {
    const svgContent = `
      <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
        <rect width="800" height="600" fill="${color}"/>
        <text x="400" y="300" text-anchor="middle" font-family="Arial" font-size="48" fill="white">
          Sample Image
        </text>
        <text x="400" y="360" text-anchor="middle" font-family="Arial" font-size="24" fill="white">
          ${filename}
        </text>
      </svg>
    `;
    
    const filepath = path.join(uploadsDir, filename.replace('.jpg', '.svg'));
    fs.writeFileSync(filepath, svgContent);
    console.log(`✅ Created SVG: ${filename.replace('.jpg', '.svg')}`);
  };

  // Create fallback images
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
  
  for (let i = 0; i < 5; i++) {
    const filename = `sample-${Date.now()}-${i + 1}.jpg`;
    createColoredImage(filename, colors[i]);
  }

  // Try to download real images (optional)
  console.log('\n🌐 Attempting to download sample images...');
  
  for (let i = 0; i < Math.min(3, sampleImages.length); i++) {
    const sample = sampleImages[i];
    const filename = `downloaded-${Date.now()}-${i + 1}.jpg`;
    const filepath = path.join(uploadsDir, filename);
    
    try {
      await downloadImage(sample.url, filepath);
      console.log(`✅ Downloaded: ${filename}`);
    } catch (error) {
      console.log(`❌ Failed to download ${sample.description}: ${error.message}`);
    }
  }

  // List final files
  const finalFiles = fs.readdirSync(uploadsDir).filter(file =>
    file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.gif') || 
    file.endsWith('.jpeg') || file.endsWith('.svg')
  );

  console.log(`\n🎉 SAMPLE IMAGES CREATED:`);
  console.log(`   Total files: ${finalFiles.length}`);
  finalFiles.forEach(file => {
    const filepath = path.join(uploadsDir, file);
    const stats = fs.statSync(filepath);
    console.log(`   - ${file} (${(stats.size / 1024).toFixed(1)}KB)`);
  });
}

// Download image helper
function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filepath);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Delete partial file
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

createSampleImages().catch(console.error);
