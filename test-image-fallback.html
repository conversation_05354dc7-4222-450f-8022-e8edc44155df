<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Fallback Fixed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-test {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #fafafa;
        }
        .image-test h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .image-container {
            position: relative;
            width: 100%;
            height: 150px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
            background: #f8f9fa;
        }
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: opacity 0.2s;
        }
        .loading {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            color: #6c757d;
            font-size: 12px;
        }
        .error-placeholder {
            width: 100%;
            height: 100%;
            background: #f8d7da;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #721c24;
            font-size: 12px;
            text-align: center;
        }
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
            margin-top: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Image Fallback - Fixed Version</h1>
        <p>Testing improved ImageWithFallback component with loop prevention</p>
        
        <button onclick="runTests()">Run Tests</button>
        <button onclick="clearLogs()">Clear Logs</button>
        
        <div class="test-grid" id="testGrid"></div>
        
        <div class="log" id="logContainer"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:5001';
        let logContainer;
        
        function log(message, type = 'info') {
            if (!logContainer) logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#333';
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }
        
        class ImageWithFallback {
            constructor(container, src, alt, testName) {
                this.container = container;
                this.originalSrc = src;
                this.alt = alt;
                this.testName = testName;
                this.attemptCount = 0;
                this.maxAttempts = 3;
                this.hasError = false;
                this.isLoading = true;
                
                this.init();
            }
            
            init() {
                this.container.innerHTML = `
                    <h4>${this.testName}</h4>
                    <div class="image-container">
                        <div class="loading">Đang tải...</div>
                        <img style="opacity: 0;" alt="${this.alt}">
                    </div>
                    <div class="status loading">Initializing...</div>
                `;
                
                this.img = this.container.querySelector('img');
                this.loadingDiv = this.container.querySelector('.loading');
                this.statusDiv = this.container.querySelector('.status');
                
                this.img.onload = () => this.handleLoad();
                this.img.onerror = () => this.handleError();
                
                this.loadImage(this.getImageUrl(this.originalSrc));
            }
            
            getImageUrl(imagePath) {
                if (!imagePath) return '';
                if (imagePath.startsWith('http')) return imagePath;
                if (imagePath.startsWith('/uploads')) return `${API_URL}${imagePath}`;
                if (imagePath.startsWith('uploads')) return `${API_URL}/${imagePath}`;
                return `${API_URL}/uploads/${imagePath}`;
            }
            
            getFallbackUrls(originalUrl) {
                const fallbacks = [];
                
                if (originalUrl.includes('/uploads/')) {
                    const filename = originalUrl.split('/').pop() || '';
                    
                    // If filename has UUID, try without UUID
                    if (filename.includes('-') && filename.split('-').length > 2) {
                        const parts = filename.split('-');
                        const simpleFilename = `${parts[0]}-${parts[1]}.jpg`;
                        const fallbackUrl = `${API_URL}/uploads/${simpleFilename}`;
                        
                        if (fallbackUrl !== originalUrl) {
                            fallbacks.push(fallbackUrl);
                        }
                    }
                    
                    // Try with different extensions
                    const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
                    const pngUrl = `${API_URL}/uploads/${nameWithoutExt}.png`;
                    const jpegUrl = `${API_URL}/uploads/${nameWithoutExt}.jpeg`;
                    
                    if (pngUrl !== originalUrl && !fallbacks.includes(pngUrl)) {
                        fallbacks.push(pngUrl);
                    }
                    if (jpegUrl !== originalUrl && !fallbacks.includes(jpegUrl)) {
                        fallbacks.push(jpegUrl);
                    }
                }
                
                return [...new Set(fallbacks)].slice(0, 2);
            }
            
            loadImage(src) {
                log(`[${this.testName}] Attempting to load: ${src}`);
                this.statusDiv.textContent = `Attempt ${this.attemptCount + 1}: Loading...`;
                this.img.src = src;
            }
            
            handleLoad() {
                log(`[${this.testName}] ✅ Image loaded successfully: ${this.img.src}`, 'success');
                this.isLoading = false;
                this.hasError = false;
                
                this.loadingDiv.style.display = 'none';
                this.img.style.opacity = '1';
                this.statusDiv.className = 'status success';
                this.statusDiv.textContent = `✅ Loaded (attempt ${this.attemptCount + 1})`;
            }
            
            handleError() {
                log(`[${this.testName}] ❌ Failed to load: ${this.img.src}`, 'error');
                
                // Prevent infinite loops
                if (this.attemptCount >= this.maxAttempts) {
                    log(`[${this.testName}] Max attempts reached`, 'error');
                    this.showError();
                    return;
                }
                
                // Try fallback URLs
                const fallbackUrls = this.getFallbackUrls(this.img.src);
                
                if (this.attemptCount < fallbackUrls.length) {
                    const fallbackUrl = fallbackUrls[this.attemptCount];
                    
                    if (fallbackUrl !== this.img.src) {
                        this.attemptCount++;
                        setTimeout(() => {
                            this.loadImage(fallbackUrl);
                        }, 100);
                        return;
                    }
                }
                
                // All fallbacks failed
                log(`[${this.testName}] All fallback attempts failed`, 'error');
                this.showError();
            }
            
            showError() {
                this.hasError = true;
                this.isLoading = false;
                
                this.loadingDiv.style.display = 'none';
                this.img.style.display = 'none';
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-placeholder';
                errorDiv.textContent = 'Hình ảnh không tải được';
                this.container.querySelector('.image-container').appendChild(errorDiv);
                
                this.statusDiv.className = 'status error';
                this.statusDiv.textContent = `❌ Failed after ${this.attemptCount + 1} attempts`;
            }
        }
        
        function runTests() {
            const testGrid = document.getElementById('testGrid');
            testGrid.innerHTML = '';
            clearLogs();
            
            log('Starting image fallback tests...');
            
            const testCases = [
                {
                    name: 'UUID File (Should Fallback)',
                    src: 'http://localhost:5001/uploads/images-1749097293728-56c1ca02-e672-41c8-9607-d11321a870d3.jpg'
                },
                {
                    name: 'Another UUID File',
                    src: 'http://localhost:5001/uploads/images-1749097293748-5f8e4de7-a070-49eb-ba37-8304cfc84223.jpg'
                },
                {
                    name: 'Valid File (Should Work)',
                    src: 'http://localhost:5001/uploads/images-1747917385255.jpg'
                },
                {
                    name: 'Non-existent File',
                    src: 'http://localhost:5001/uploads/nonexistent-file.jpg'
                },
                {
                    name: 'Empty Source',
                    src: ''
                }
            ];
            
            testCases.forEach((testCase, index) => {
                const testDiv = document.createElement('div');
                testDiv.className = 'image-test';
                testGrid.appendChild(testDiv);
                
                setTimeout(() => {
                    new ImageWithFallback(testDiv, testCase.src, `Test ${index + 1}`, testCase.name);
                }, index * 200); // Stagger the tests
            });
        }
        
        // Auto-run tests on page load
        window.onload = () => {
            log('Page loaded - ready for testing');
            setTimeout(runTests, 1000);
        };
    </script>
</body>
</html>
