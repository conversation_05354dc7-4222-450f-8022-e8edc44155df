<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Complete Image Upload System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .image-item {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }
        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
        .image-item.error {
            border-color: #dc3545;
        }
        .image-item.success {
            border-color: #28a745;
        }
        .image-status {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Complete Image Upload System</h1>
        <p>Kiểm tra toàn bộ hệ thống upload và hiển thị hình ảnh</p>

        <!-- Backend Connection Test -->
        <div class="test-section">
            <h3>🔌 Backend Connection Test</h3>
            <button onclick="testBackendConnection()">Test Backend</button>
            <div id="backendStatus" class="status info">Chưa test</div>
        </div>

        <!-- Static Files Test -->
        <div class="test-section">
            <h3>📁 Static Files Test</h3>
            <button onclick="testStaticFiles()">Test Static Files</button>
            <div id="staticStatus" class="status info">Chưa test</div>
            <div id="staticResults" class="image-grid"></div>
        </div>

        <!-- Image Fallback Test -->
        <div class="test-section">
            <h3>🔄 Image Fallback Test</h3>
            <button onclick="testImageFallback()">Test Fallback Logic</button>
            <div id="fallbackStatus" class="status info">Chưa test</div>
            <div id="fallbackResults" class="image-grid"></div>
        </div>

        <!-- Upload Simulation -->
        <div class="test-section">
            <h3>📤 Upload Simulation</h3>
            <input type="file" id="testUpload" multiple accept="image/*">
            <button onclick="simulateUpload()">Simulate Upload</button>
            <div id="uploadStatus" class="status info">Chưa test</div>
            <div id="uploadResults" class="image-grid"></div>
        </div>

        <!-- Test Log -->
        <div class="test-section">
            <h3>📋 Test Log</h3>
            <button onclick="clearLog()" class="danger">Clear Log</button>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:5001';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('testLog');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#333';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }

        async function testBackendConnection() {
            const statusDiv = document.getElementById('backendStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = 'Testing...';
            
            try {
                log('Testing backend connection...');
                const response = await fetch(`${API_URL}/api/campaigns/fix-images`);
                
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `✅ Backend connected! Status: ${response.status}`;
                    log(`Backend response: ${JSON.stringify(data)}`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Backend connection failed: ${error.message}`;
                log(`Backend connection error: ${error.message}`, 'error');
            }
        }

        async function testStaticFiles() {
            const statusDiv = document.getElementById('staticStatus');
            const resultsDiv = document.getElementById('staticResults');
            
            statusDiv.className = 'status info';
            statusDiv.textContent = 'Testing static files...';
            resultsDiv.innerHTML = '';
            
            const testFiles = [
                'images-1747917385255.jpg',
                'images-1747917385280.jpg',
                'images-1748012855368.jpg',
                'images-1748013928098.jpg',
                'images-1748014182146.jpg',
                'images-1748051570641.jpg'
            ];
            
            let successCount = 0;
            let errorCount = 0;
            
            for (const filename of testFiles) {
                const url = `${API_URL}/uploads/${filename}`;
                const imageDiv = document.createElement('div');
                imageDiv.className = 'image-item';
                
                const img = document.createElement('img');
                img.src = url;
                img.alt = filename;
                
                const statusSpan = document.createElement('div');
                statusSpan.className = 'image-status';
                statusSpan.textContent = 'Loading...';
                
                img.onload = () => {
                    imageDiv.className = 'image-item success';
                    statusSpan.textContent = '✅ Loaded';
                    successCount++;
                    log(`File loaded: ${filename}`, 'success');
                    updateStaticStatus();
                };
                
                img.onerror = () => {
                    imageDiv.className = 'image-item error';
                    statusSpan.textContent = '❌ Failed';
                    errorCount++;
                    log(`File failed: ${filename}`, 'error');
                    updateStaticStatus();
                };
                
                imageDiv.appendChild(img);
                imageDiv.appendChild(statusSpan);
                resultsDiv.appendChild(imageDiv);
            }
            
            function updateStaticStatus() {
                if (successCount + errorCount === testFiles.length) {
                    if (successCount > 0) {
                        statusDiv.className = 'status success';
                        statusDiv.textContent = `✅ ${successCount}/${testFiles.length} files loaded successfully`;
                    } else {
                        statusDiv.className = 'status error';
                        statusDiv.textContent = `❌ All files failed to load`;
                    }
                }
            }
        }

        function testImageFallback() {
            const statusDiv = document.getElementById('fallbackStatus');
            const resultsDiv = document.getElementById('fallbackResults');
            
            statusDiv.className = 'status info';
            statusDiv.textContent = 'Testing fallback logic...';
            resultsDiv.innerHTML = '';
            
            // Test with broken URLs that should fallback
            const testUrls = [
                'http://localhost:5001/uploads/images-1749097293728-56c1ca02-e672-41c8-9607-d11321a870d3.jpg',
                'http://localhost:5001/uploads/images-1749097293748-5f8e4de7-a070-49eb-ba37-8304cfc84223.jpg',
                'http://localhost:5001/uploads/nonexistent-file.jpg'
            ];
            
            testUrls.forEach((url, index) => {
                const imageDiv = document.createElement('div');
                imageDiv.className = 'image-item';
                
                const img = document.createElement('img');
                img.src = url;
                img.alt = `Fallback test ${index + 1}`;
                
                const statusSpan = document.createElement('div');
                statusSpan.className = 'image-status';
                statusSpan.textContent = 'Testing fallback...';
                
                let attemptCount = 0;
                
                img.onerror = () => {
                    attemptCount++;
                    log(`Fallback attempt ${attemptCount} for: ${url}`, 'info');
                    
                    // Simulate fallback logic
                    if (url.includes('/uploads/') && url.includes('-')) {
                        const filename = url.split('/').pop();
                        if (filename && filename.includes('-') && filename.split('-').length > 2) {
                            const parts = filename.split('-');
                            const simpleFilename = `${parts[0]}-${parts[1]}.jpg`;
                            const fallbackUrl = `${API_URL}/uploads/${simpleFilename}`;
                            
                            if (img.src !== fallbackUrl && attemptCount < 3) {
                                log(`Trying fallback: ${fallbackUrl}`, 'info');
                                img.src = fallbackUrl;
                                return;
                            }
                        }
                    }
                    
                    // All fallbacks failed
                    imageDiv.className = 'image-item error';
                    statusSpan.textContent = '❌ All fallbacks failed';
                    img.style.display = 'none';
                    
                    const placeholder = document.createElement('div');
                    placeholder.style.cssText = 'width: 100%; height: 150px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #6c757d; font-size: 14px;';
                    placeholder.textContent = 'Hình ảnh không tải được';
                    imageDiv.insertBefore(placeholder, statusSpan);
                    
                    log(`All fallbacks failed for: ${url}`, 'error');
                };
                
                img.onload = () => {
                    imageDiv.className = 'image-item success';
                    statusSpan.textContent = `✅ Loaded (attempt ${attemptCount + 1})`;
                    log(`Image loaded successfully: ${img.src}`, 'success');
                };
                
                imageDiv.appendChild(img);
                imageDiv.appendChild(statusSpan);
                resultsDiv.appendChild(imageDiv);
            });
            
            statusDiv.className = 'status success';
            statusDiv.textContent = '✅ Fallback test started - check results below';
        }

        function simulateUpload() {
            const fileInput = document.getElementById('testUpload');
            const statusDiv = document.getElementById('uploadStatus');
            const resultsDiv = document.getElementById('uploadResults');
            
            if (!fileInput.files.length) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Please select files first';
                return;
            }
            
            statusDiv.className = 'status info';
            statusDiv.textContent = 'Simulating upload...';
            resultsDiv.innerHTML = '';
            
            Array.from(fileInput.files).forEach((file, index) => {
                const imageDiv = document.createElement('div');
                imageDiv.className = 'image-item';
                
                const img = document.createElement('img');
                const statusSpan = document.createElement('div');
                statusSpan.className = 'image-status';
                statusSpan.textContent = 'Processing...';
                
                // Validate file
                if (file.size > 10 * 1024 * 1024) {
                    imageDiv.className = 'image-item error';
                    statusSpan.textContent = '❌ File too large (>10MB)';
                    log(`File too large: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`, 'error');
                } else if (!file.type.startsWith('image/')) {
                    imageDiv.className = 'image-item error';
                    statusSpan.textContent = '❌ Not an image file';
                    log(`Invalid file type: ${file.name} (${file.type})`, 'error');
                } else {
                    // Create preview
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        img.src = e.target.result;
                        imageDiv.className = 'image-item success';
                        statusSpan.textContent = `✅ Ready to upload (${(file.size / 1024).toFixed(1)}KB)`;
                        log(`File ready: ${file.name}`, 'success');
                    };
                    reader.readAsDataURL(file);
                }
                
                img.alt = file.name;
                imageDiv.appendChild(img);
                imageDiv.appendChild(statusSpan);
                resultsDiv.appendChild(imageDiv);
            });
            
            statusDiv.className = 'status success';
            statusDiv.textContent = `✅ Processed ${fileInput.files.length} files`;
        }

        // Auto-run basic tests on page load
        window.onload = () => {
            log('Page loaded - ready for testing');
            setTimeout(testBackendConnection, 1000);
        };
    </script>
</body>
</html>
