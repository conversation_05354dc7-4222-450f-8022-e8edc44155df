import express from 'express';
import { Campaign } from '../models/Campaign';
import { Donation } from '../models/Donation';
import { User } from '../models/user.model';

const router = express.Router();

// Get real homepage statistics
router.get('/statistics', async (req, res) => {
  try {
    console.log('📊 Fetching real homepage statistics...');

    // Import Event model dynamically
    const Event = require('../models/Event').default;

    // Get real statistics from database
    const [totalCampaigns, totalEvents, totalDonationAmount, totalUsers] = await Promise.all([
      Campaign.countDocuments(), // Count all campaigns including completed ones
      Event.countDocuments(),
      Donation.aggregate([
        { $match: { status: 'success' } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]),
      User.countDocuments({ role: 'user' })
    ]);

    const statistics = {
      totalEvents,
      totalCampaigns,
      totalParticipants: totalUsers, // Tình nguyện viên = users
      totalDonations: totalDonationAmount[0]?.total || 0
    };

    console.log('✅ Homepage statistics:', statistics);

    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('❌ Error fetching homepage statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Không thể tải thống kê trang chủ'
    });
  }
});

// Get all successful donations list
router.get('/donations', async (req, res) => {
  try {
    console.log('💰 Fetching donations list...');

    const donations = await Donation.find({ status: 'success' })
      .populate('campaignId', 'title')
      .sort({ createdAt: -1 })
      .limit(100)
      .select('name email amount message createdAt campaignId isAnonymous');

    const donationsList = donations.map(donation => ({
      _id: donation._id,
      name: donation.isAnonymous ? 'Người ủng hộ ẩn danh' : donation.name,
      email: donation.isAnonymous ? '***@***.***' : `${donation.email.substring(0, 3)}***@${donation.email.split('@')[1]}`,
      amount: donation.amount,
      message: donation.message || '',
      createdAt: donation.createdAt,
      campaign: {
        _id: donation.campaignId._id,
        title: donation.campaignId.title
      }
    }));

    console.log(`✅ Found ${donationsList.length} donations`);

    res.json({
      success: true,
      data: donationsList
    });
  } catch (error) {
    console.error('❌ Error fetching donations:', error);
    res.status(500).json({
      success: false,
      message: 'Không thể tải danh sách quyên góp'
    });
  }
});

// Get volunteers (users) list
router.get('/volunteers', async (req, res) => {
  try {
    console.log('👥 Fetching volunteers list...');

    const volunteers = await User.find({ role: 'user' })
      .sort({ createdAt: -1 })
      .limit(100)
      .select('name email createdAt');

    const volunteersList = volunteers.map(user => ({
      _id: user._id,
      name: user.name,
      email: `${user.email.substring(0, 3)}***@${user.email.split('@')[1]}`,
      createdAt: user.createdAt
    }));

    console.log(`✅ Found ${volunteersList.length} volunteers`);

    res.json({
      success: true,
      data: volunteersList
    });
  } catch (error) {
    console.error('❌ Error fetching volunteers:', error);
    res.status(500).json({
      success: false,
      message: 'Không thể tải danh sách tình nguyện viên'
    });
  }
});

// Featured events endpoint
router.get('/featured-events', async (req, res) => {
  try {
    console.log('🌟 Fetching featured events...');

    // Import Event model dynamically
    const Event = require('../models/Event').default;

    // Get events with high registrations or good ratings
    const featuredEvents = await Event.aggregate([
      {
        $addFields: {
          participantCount: { $size: { $ifNull: ['$participants', []] } },
          averageRating: { $ifNull: ['$averageRating', 0] },
          priority: {
            $cond: {
              if: { $eq: ['$status', 'upcoming'] },
              then: 2, // Higher priority for upcoming events
              else: 1
            }
          }
        }
      },
      {
        $sort: {
          priority: -1, // Upcoming events first
          participantCount: -1, // Then by participant count
          averageRating: -1 // Then by rating
        }
      },
      {
        $limit: 6
      },
      {
        $project: {
          title: 1,
          description: 1,
          startDate: 1,
          endDate: 1,
          location: 1,
          maxParticipants: 1,
          participantCount: 1,
          averageRating: 1,
          status: 1,
          category: 1,
          organizerName: 1
        }
      }
    ]);

    console.log(`✅ Found ${featuredEvents.length} featured events`);

    res.json({
      success: true,
      data: featuredEvents
    });
  } catch (error) {
    console.error('❌ Error fetching featured events:', error);
    res.status(500).json({
      success: false,
      message: 'Không thể tải sự kiện nổi bật'
    });
  }
});

// Featured campaigns endpoint
router.get('/featured-campaigns', async (req, res) => {
  try {
    console.log('🌟 Fetching featured campaigns...');

    // Get campaigns with high donations or completed status, prioritize active ones
    const featuredCampaigns = await Campaign.aggregate([
      {
        $addFields: {
          progressPercentage: {
            $multiply: [
              { $divide: ['$currentAmount', '$targetAmount'] },
              100
            ]
          },
          priority: {
            $cond: {
              if: { $eq: ['$status', 'active'] },
              then: 2, // Higher priority for active campaigns
              else: 1
            }
          }
        }
      },
      {
        $sort: {
          priority: -1, // Active campaigns first
          currentAmount: -1, // Then by donation amount
          progressPercentage: -1 // Then by progress
        }
      },
      {
        $limit: 6
      },
      {
        $project: {
          title: 1,
          description: 1,
          targetAmount: 1,
          currentAmount: 1,
          status: 1,
          category: 1,
          startDate: 1,
          endDate: 1,
          organizationName: 1,
          progressPercentage: 1,
          totalDonors: { $ifNull: ['$totalDonors', 0] }
        }
      }
    ]);

    console.log(`✅ Found ${featuredCampaigns.length} featured campaigns`);

    res.json({
      success: true,
      data: featuredCampaigns
    });
  } catch (error) {
    console.error('❌ Error fetching featured campaigns:', error);
    res.status(500).json({
      success: false,
      message: 'Không thể tải chiến dịch nổi bật'
    });
  }
});

// Latest posts endpoint
router.get('/latest-posts', async (req, res) => {
  try {
    // Return empty array for now - can be enhanced later
    const latestPosts: any[] = [];

    res.json({
      success: true,
      data: latestPosts
    });
  } catch (error) {
    console.error('Error fetching latest posts:', error);
    res.status(500).json({
      success: false,
      message: 'Không thể tải bài viết mới nhất'
    });
  }
});

export default router;