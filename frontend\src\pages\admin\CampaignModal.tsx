import React, { useState, useEffect, useRef } from 'react';
import { X, Upload, Calendar, DollarSign, Trash2, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import axios from 'axios';
import SafeImage from '../../components/ui/SafeImage';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface Campaign {
  _id?: string;
  title: string;
  description: string;
  targetAmount: number;
  startDate: string;
  endDate: string;
  category?: string;
  status: 'active' | 'completed' | 'cancelled' | 'draft';
  images: string[];
}

interface CampaignModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  campaign?: Campaign | null;
}

const CampaignModal: React.FC<CampaignModalProps> = ({ isOpen, onClose, onSave, campaign }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    targetAmount: '',
    startDate: '',
    endDate: '',
    category: '',
    status: 'draft' as 'active' | 'completed' | 'cancelled' | 'draft'
  });
  
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [existingImages, setExistingImages] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [replacingIndex, setReplacingIndex] = useState<number | null>(null);

  // Helper function to get correct image URL with fallback
  const getImageUrl = (imagePath: string) => {
    if (!imagePath) return '';

    // Handle data URLs (base64 images) - return as is
    if (imagePath.startsWith('data:')) return imagePath;

    // Handle blob URLs - return as is
    if (imagePath.startsWith('blob:')) return imagePath;

    // If it's already a full URL (starts with http), return as is
    if (imagePath.startsWith('http')) {
      return imagePath;
    }

    // If it starts with /uploads, prepend API_URL
    if (imagePath.startsWith('/uploads')) {
      return `${API_URL}${imagePath}`;
    }

    // If it starts with uploads (without /), prepend API_URL with /
    if (imagePath.startsWith('uploads')) {
      return `${API_URL}/${imagePath}`;
    }

    // Default case: assume it's a filename only
    return `${API_URL}/uploads/${imagePath}`;
  };

  // Helper function to get fallback image URLs
  const getFallbackUrls = (originalUrl: string): string[] => {
    const fallbacks: string[] = [];

    if (originalUrl.includes('/uploads/')) {
      const filename = originalUrl.split('/').pop() || '';

      // If filename has UUID, try without UUID
      if (filename.includes('-') && filename.split('-').length > 2) {
        const parts = filename.split('-');
        const simpleFilename = `${parts[0]}-${parts[1]}.jpg`;
        fallbacks.push(`${API_URL}/uploads/${simpleFilename}`);
      }

      // Try with different extensions
      const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
      fallbacks.push(`${API_URL}/uploads/${nameWithoutExt}.png`);
      fallbacks.push(`${API_URL}/uploads/${nameWithoutExt}.jpeg`);
    }

    return fallbacks;
  };

  useEffect(() => {
    if (isOpen) {
      if (campaign) {
        // Edit mode
        console.log('🔄 [CampaignModal] Loading campaign for edit:', campaign.title);
        console.log('🖼️ [CampaignModal] Campaign images:', campaign.images);
        console.log('🖼️ [CampaignModal] Campaign images length:', campaign.images?.length || 0);

        setFormData({
          title: campaign.title,
          description: campaign.description,
          targetAmount: campaign.targetAmount.toString(),
          startDate: campaign.startDate.split('T')[0],
          endDate: campaign.endDate.split('T')[0],
          category: campaign.category || '',
          status: campaign.status
        });

        const campaignImages = campaign.images || [];
        const previewUrls = campaignImages.map(img => {
          const url = getImageUrl(img);
          console.log(`🖼️ [CampaignModal] Image: ${img} -> URL: ${url}`);
          return url;
        });

        setExistingImages(campaignImages);
        setImagePreviewUrls(previewUrls);

        console.log('🖼️ [CampaignModal] Set existingImages:', campaignImages);
        console.log('🖼️ [CampaignModal] Set previewUrls:', previewUrls);
      } else {
        // Create mode
        resetForm();
      }
    } else {
      // Modal closed - clear all state
      console.log('🔄 [CampaignModal] Modal closed, clearing state');
      resetForm();
    }
  }, [campaign, isOpen]);

  const resetForm = () => {
    console.log('🔄 [CampaignModal] Resetting form');
    setFormData({
      title: '',
      description: '',
      targetAmount: '',
      startDate: '',
      endDate: '',
      category: '',
      status: 'draft'
    });
    setSelectedImages([]);
    setImagePreviewUrls([]);
    setExistingImages([]);
    setReplacingIndex(null);
    console.log('✅ [CampaignModal] Form reset complete');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const totalImages = selectedImages.length + existingImages.length;

    if (files.length + totalImages > 5) {
      toast.error('Tối đa 5 hình ảnh');
      return;
    }

    // Validate file sizes
    const maxSize = 10 * 1024 * 1024; // 10MB
    const invalidFiles = files.filter(file => file.size > maxSize);
    if (invalidFiles.length > 0) {
      toast.error(`Một số file quá lớn. Kích thước tối đa: 10MB`);
      return;
    }

    setSelectedImages(prev => [...prev, ...files]);

    // Create preview URLs for new files
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviewUrls(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });

    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  const removeImage = (index: number) => {
    const totalExisting = existingImages.length;

    if (index < totalExisting) {
      // Remove existing image
      setExistingImages(prev => prev.filter((_, i) => i !== index));
    } else {
      // Remove new image
      const newImageIndex = index - totalExisting;
      setSelectedImages(prev => prev.filter((_, i) => i !== newImageIndex));
    }

    setImagePreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  const replaceImage = (index: number) => {
    setReplacingIndex(index);
    fileInputRef.current?.click();
  };

  const handleReplaceImage = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || replacingIndex === null) return;

    // Validate file size
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      toast.error('File quá lớn. Kích thước tối đa: 10MB');
      return;
    }

    const totalExisting = existingImages.length;

    if (replacingIndex < totalExisting) {
      // Replace existing image - remove from existing and add to new
      setExistingImages(prev => prev.filter((_, i) => i !== replacingIndex));
      setSelectedImages(prev => [...prev, file]);
    } else {
      // Replace new image
      const newImageIndex = replacingIndex - totalExisting;
      setSelectedImages(prev => {
        const newImages = [...prev];
        newImages[newImageIndex] = file;
        return newImages;
      });
    }

    // Update preview URL
    setImagePreviewUrls(prev => {
      const newUrls = [...prev];
      newUrls[replacingIndex] = URL.createObjectURL(file);
      return newUrls;
    });

    setReplacingIndex(null);
    e.target.value = '';
    toast.success('Đã thay thế hình ảnh');
  };

  const validateForm = () => {
    if (!formData.title.trim()) {
      toast.error('Vui lòng nhập tiêu đề chiến dịch');
      return false;
    }
    if (!formData.description.trim()) {
      toast.error('Vui lòng nhập mô tả chiến dịch');
      return false;
    }
    if (!formData.targetAmount || parseFloat(formData.targetAmount) < 10000) {
      toast.error('Số tiền mục tiêu phải ít nhất 10,000 VNĐ');
      return false;
    }
    if (!formData.startDate) {
      toast.error('Vui lòng chọn ngày bắt đầu');
      return false;
    }
    if (!formData.endDate) {
      toast.error('Vui lòng chọn ngày kết thúc');
      return false;
    }
    if (new Date(formData.endDate) <= new Date(formData.startDate)) {
      toast.error('Ngày kết thúc phải sau ngày bắt đầu');
      return false;
    }
    // Images are optional now
    // if (existingImages.length + selectedImages.length === 0) {
    //   toast.error('Vui lòng thêm ít nhất 1 hình ảnh');
    //   return false;
    // }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🎯 [CampaignModal] Form submitted');

    if (!validateForm()) {
      console.log('❌ [CampaignModal] Form validation failed');
      return;
    }

    console.log('✅ [CampaignModal] Form validation passed');
    setLoading(true);
    try {
      console.log('🚀 [CampaignModal] Starting form submission');
      console.log('📝 [CampaignModal] Form data:', formData);
      console.log('🖼️ [CampaignModal] Selected images:', selectedImages.length);
      console.log('🖼️ [CampaignModal] Existing images:', existingImages.length);
      console.log('🌐 [CampaignModal] API_URL:', API_URL);

      const token = localStorage.getItem('token');
      const formDataToSend = new FormData();
      
      formDataToSend.append('title', formData.title);
      formDataToSend.append('description', formData.description);
      formDataToSend.append('targetAmount', formData.targetAmount);
      formDataToSend.append('startDate', formData.startDate);
      formDataToSend.append('endDate', formData.endDate);
      formDataToSend.append('category', formData.category || 'Khác');
      // Note: status is set automatically by backend, don't send it

      // Add existing images (only for updates)
      if (campaign?._id) {
        console.log('🖼️ [CampaignModal] Adding existing images to FormData:', existingImages);
        existingImages.forEach((image, index) => {
          console.log(`🖼️ [CampaignModal] Adding existing image ${index}:`, image);
          formDataToSend.append('existingImages', image);
        });
      }

      // Add new images
      selectedImages.forEach(image => {
        formDataToSend.append('images', image);
      });

      if (campaign?._id) {
        // Update campaign
        console.log('🔄 [CampaignModal] Updating campaign with ID:', campaign._id);
        const response = await axios.put(`${API_URL}/api/campaigns/admin/${campaign._id}`, formDataToSend, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          }
        });
        console.log('✅ [CampaignModal] Update response:', response.data);
        toast.success('Cập nhật chiến dịch thành công');
      } else {
        // Create campaign
        console.log('🆕 [CampaignModal] Creating new campaign');
        console.log('🔗 [CampaignModal] Request URL:', `${API_URL}/api/campaigns/admin`);
        console.log('🔑 [CampaignModal] Token:', token ? 'Present' : 'Missing');

        const response = await axios.post(`${API_URL}/api/campaigns/admin`, formDataToSend, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          }
        });
        console.log('✅ [CampaignModal] Create response:', response.data);
        toast.success('Tạo chiến dịch thành công');
      }
      
      onSave();
      onClose();
    } catch (error: any) {
      console.error('Error saving campaign:', error);
      toast.error(error.response?.data?.message || 'Có lỗi xảy ra khi lưu chiến dịch');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {campaign ? 'Chỉnh sửa chiến dịch' : 'Tạo chiến dịch mới'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              Tiêu đề chiến dịch *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Nhập tiêu đề chiến dịch..."
              required
            />
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Mô tả chiến dịch *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Nhập mô tả chi tiết về chiến dịch..."
              required
            />
          </div>

          {/* Target Amount */}
          <div>
            <label htmlFor="targetAmount" className="block text-sm font-medium text-gray-700 mb-2">
              Số tiền mục tiêu (VNĐ) *
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="number"
                id="targetAmount"
                name="targetAmount"
                value={formData.targetAmount}
                onChange={handleInputChange}
                min="10000"
                step="10000"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="10,000"
                required
              />
            </div>
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
              Danh mục
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Chọn danh mục</option>
              <option value="Giáo dục">Giáo dục</option>
              <option value="Y tế">Y tế</option>
              <option value="Môi trường">Môi trường</option>
              <option value="Trẻ em">Trẻ em</option>
              <option value="Người già">Người già</option>
              <option value="Động vật">Động vật</option>
              <option value="Cộng đồng">Cộng đồng</option>
              <option value="Khác">Khác</option>
            </select>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-2">
                Ngày bắt đầu *
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-2">
                Ngày kết thúc *
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  value={formData.endDate}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
            </div>
          </div>

          {/* Status */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
              Trạng thái
            </label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="draft">Bản nháp</option>
              <option value="active">Đang hoạt động</option>
              <option value="completed">Đã hoàn thành</option>
              <option value="cancelled">Đã hủy</option>
            </select>
          </div>

          {/* Images */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label htmlFor="image-upload" className="block text-sm font-medium text-gray-700">
                Hình ảnh chiến dịch
              </label>
              <span className="text-xs text-gray-500">
                {existingImages.length + selectedImages.length}/5 ảnh
              </span>
            </div>

            {/* Image Upload */}
            <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              existingImages.length + selectedImages.length >= 5
                ? 'border-gray-200 bg-gray-50 cursor-not-allowed'
                : 'border-gray-300 hover:border-gray-400 cursor-pointer'
            }`}>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
                id="image-upload"
                name="images"
                disabled={existingImages.length + selectedImages.length >= 5}
              />
              {/* Hidden input for replacing images */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleReplaceImage}
                className="hidden"
              />
              <label
                htmlFor="image-upload"
                className={`${existingImages.length + selectedImages.length >= 5 ? 'cursor-not-allowed' : 'cursor-pointer'}`}
              >
                <Upload className={`mx-auto h-12 w-12 ${
                  existingImages.length + selectedImages.length >= 5 ? 'text-gray-300' : 'text-gray-400'
                }`} />
                <p className={`mt-2 text-sm ${
                  existingImages.length + selectedImages.length >= 5 ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {existingImages.length + selectedImages.length >= 5
                    ? 'Đã đạt giới hạn tối đa 5 ảnh'
                    : 'Nhấp để chọn ảnh hoặc kéo thả ảnh vào đây'
                  }
                </p>
                <p className="text-xs text-gray-500">PNG, JPG, GIF tối đa 10MB</p>
              </label>
            </div>

            {/* Image Previews */}
            {imagePreviewUrls.length > 0 && (
              <div className="mt-4">
                <div className="mb-2 text-sm text-gray-600">
                  Hình ảnh đã chọn ({imagePreviewUrls.length})
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {imagePreviewUrls.map((url, index) => {
                    // Determine if this is an existing image based on current arrays
                    const isExisting = index < existingImages.length;
                    const isNewImage = !isExisting;

                    return (
                      <div key={`${index}-${isExisting ? 'existing' : 'new'}`} className="relative group">
                        <SafeImage
                          src={url}
                          alt={`Preview ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg border border-gray-200"
                        />

                        {/* Image type indicator */}
                        <div className="absolute top-1 left-1">
                          <span className={`px-1.5 py-0.5 text-xs rounded text-white ${
                            isExisting ? 'bg-green-500' : 'bg-blue-500'
                          }`}>
                            {isExisting ? 'Có sẵn' : 'Mới'}
                          </span>
                        </div>
                    {/* Action buttons */}
                    <div className="absolute -top-2 -right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-200">
                      <button
                        type="button"
                        onClick={() => replaceImage(index)}
                        className="bg-blue-500 hover:bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center shadow-lg transition-all duration-200"
                        title="Thay thế hình ảnh"
                      >
                        <RefreshCw className="h-3 w-3" />
                      </button>
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center shadow-lg transition-all duration-200"
                        title="Xóa hình ảnh"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Hủy
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'Đang lưu...' : (campaign ? 'Cập nhật' : 'Tạo chiến dịch')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CampaignModal;
