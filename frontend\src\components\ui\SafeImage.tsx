import React from 'react';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface SafeImageProps {
  src: string;
  alt: string;
  className?: string;
}

const SafeImage: React.FC<SafeImageProps> = ({ src, alt, className = '' }) => {
  // Simple and direct URL processing
  const getImageUrl = (imagePath: string): string => {
    console.log('🖼️ [SafeImage] Input path:', imagePath);
    console.log('🖼️ [SafeImage] API_URL:', API_URL);

    if (!imagePath) {
      console.log('🖼️ [SafeImage] Empty path');
      return '';
    }

    // Handle data URLs (base64 images) - return as is
    if (imagePath.startsWith('data:')) {
      console.log('🖼️ [SafeImage] Data URL detected');
      return imagePath;
    }

    // Handle blob URLs - return as is
    if (imagePath.startsWith('blob:')) {
      console.log('🖼️ [SafeImage] Blob URL detected');
      return imagePath;
    }

    // Handle full HTTP URLs - return as is
    if (imagePath.startsWith('http')) {
      console.log('🖼️ [SafeImage] HTTP URL detected');
      return imagePath;
    }

    // Clean up path - remove double slashes
    let cleanPath = imagePath.replace(/\/+/g, '/');
    console.log('🖼️ [SafeImage] Cleaned path:', cleanPath);

    // Always construct full URL with API_URL
    let finalUrl;
    if (cleanPath.startsWith('/')) {
      finalUrl = `${API_URL}${cleanPath}`;
    } else {
      finalUrl = `${API_URL}/${cleanPath}`;
    }

    console.log('🖼️ [SafeImage] Final URL:', finalUrl);
    return finalUrl;
  };

  const imageUrl = getImageUrl(src);

  if (!imageUrl) {
    return (
      <div className={`bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`}>
        Không có hình ảnh
      </div>
    );
  }

  return (
    <img
      src={imageUrl}
      alt={alt}
      className={className}
      onLoad={() => {
        console.log('✅ [SafeImage] Image loaded successfully:', imageUrl);
      }}
      onError={(e) => {
        const target = e.target as HTMLImageElement;
        console.error('❌ [SafeImage] Image failed to load:', {
          originalPath: src,
          finalUrl: imageUrl,
          actualSrc: target.src
        });

        target.style.display = 'none';

        // Create placeholder if not exists
        if (!target.nextElementSibling?.classList.contains('error-placeholder')) {
          const placeholder = document.createElement('div');
          placeholder.className = `error-placeholder bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`;
          placeholder.textContent = 'Hình ảnh không tải được';
          target.parentNode?.appendChild(placeholder);
        }
      }}
    />
  );
};

export default SafeImage;
