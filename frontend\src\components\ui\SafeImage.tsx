import React from 'react';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface SafeImageProps {
  src: string;
  alt: string;
  className?: string;
}

const SafeImage: React.FC<SafeImageProps> = ({ src, alt, className = '' }) => {
  // Simple URL processing without fallback attempts
  const getImageUrl = (imagePath: string): string => {
    if (!imagePath) return '';

    // Handle data URLs (base64 images) - return as is
    if (imagePath.startsWith('data:')) return imagePath;

    // Handle blob URLs - return as is
    if (imagePath.startsWith('blob:')) return imagePath;

    // Handle full HTTP URLs - return as is
    if (imagePath.startsWith('http')) return imagePath;

    // Clean up path - remove double slashes and ensure proper format
    let cleanPath = imagePath.replace(/\/+/g, '/'); // Replace multiple slashes with single slash

    // Handle relative paths
    if (cleanPath.startsWith('/uploads')) {
      return `${API_URL}${cleanPath}`;
    }
    if (cleanPath.startsWith('uploads')) {
      return `${API_URL}/${cleanPath}`;
    }

    // Default case: assume it's a filename only
    return `${API_URL}/uploads/${cleanPath}`;
  };

  const imageUrl = getImageUrl(src);

  // If no valid URL, show placeholder immediately
  if (!imageUrl) {
    return (
      <div className={`bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`}>
        Không có hình ảnh
      </div>
    );
  }

  return (
    <img
      src={imageUrl}
      alt={alt}
      className={className}
      onError={(e) => {
        // Silent error handling - replace with placeholder
        const target = e.target as HTMLImageElement;
        target.style.display = 'none';
        
        // Create placeholder if not exists
        if (!target.nextElementSibling?.classList.contains('error-placeholder')) {
          const placeholder = document.createElement('div');
          placeholder.className = `error-placeholder bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`;
          placeholder.textContent = 'Hình ảnh không tải được';
          target.parentNode?.appendChild(placeholder);
        }
      }}
    />
  );
};

export default SafeImage;
