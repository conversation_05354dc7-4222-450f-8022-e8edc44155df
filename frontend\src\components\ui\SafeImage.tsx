import React from 'react';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface SafeImageProps {
  src: string;
  alt: string;
  className?: string;
}

const SafeImage: React.FC<SafeImageProps> = ({ src, alt, className = '' }) => {
  // Simple URL processing without fallback attempts
  const getImageUrl = (imagePath: string): string => {
    console.log('🖼️ [SafeImage] Processing path:', imagePath);
    console.log('🖼️ [SafeImage] API_URL:', API_URL);

    if (!imagePath) {
      console.log('🖼️ [SafeImage] Empty path, returning empty string');
      return '';
    }

    // Handle data URLs (base64 images) - return as is
    if (imagePath.startsWith('data:')) {
      console.log('🖼️ [SafeImage] Data URL detected');
      return imagePath;
    }

    // Handle blob URLs - return as is
    if (imagePath.startsWith('blob:')) {
      console.log('🖼️ [SafeImage] Blob URL detected');
      return imagePath;
    }

    // Handle full HTTP URLs - return as is
    if (imagePath.startsWith('http')) {
      console.log('🖼️ [SafeImage] HTTP URL detected');
      return imagePath;
    }

    // Clean up path - remove double slashes and ensure proper format
    let cleanPath = imagePath.replace(/\/+/g, '/'); // Replace multiple slashes with single slash
    console.log('🖼️ [SafeImage] Cleaned path:', cleanPath);

    let finalUrl = '';

    // Handle relative paths
    if (cleanPath.startsWith('/uploads')) {
      finalUrl = `${API_URL}${cleanPath}`;
      console.log('🖼️ [SafeImage] /uploads path, final URL:', finalUrl);
      return finalUrl;
    }
    if (cleanPath.startsWith('uploads')) {
      finalUrl = `${API_URL}/${cleanPath}`;
      console.log('🖼️ [SafeImage] uploads path, final URL:', finalUrl);
      return finalUrl;
    }

    // Default case: assume it's a filename only
    finalUrl = `${API_URL}/uploads/${cleanPath}`;
    console.log('🖼️ [SafeImage] Default case, final URL:', finalUrl);
    return finalUrl;
  };

  const imageUrl = getImageUrl(src);

  // If no valid URL, show placeholder immediately
  if (!imageUrl) {
    return (
      <div className={`bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`}>
        Không có hình ảnh
      </div>
    );
  }

  return (
    <img
      src={imageUrl}
      alt={alt}
      className={className}
      onError={(e) => {
        // Debug error handling
        const target = e.target as HTMLImageElement;
        console.error('🖼️ [SafeImage] Image failed to load:', {
          src: target.src,
          originalPath: src,
          processedUrl: imageUrl,
          error: e
        });

        target.style.display = 'none';

        // Create placeholder if not exists
        if (!target.nextElementSibling?.classList.contains('error-placeholder')) {
          const placeholder = document.createElement('div');
          placeholder.className = `error-placeholder bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`;
          placeholder.textContent = 'Hình ảnh không tải được';
          target.parentNode?.appendChild(placeholder);
        }
      }}
      onLoad={() => {
        console.log('🖼️ [SafeImage] Image loaded successfully:', imageUrl);
      }}
    />
  );
};

export default SafeImage;
