import React from 'react';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface SafeImageProps {
  src: string;
  alt: string;
  className?: string;
}

const SafeImage: React.FC<SafeImageProps> = ({ src, alt, className = '' }) => {
  // Robust URL processing with comprehensive double slash handling
  const getImageUrl = (imagePath: string): string => {
    if (!imagePath) return '';

    // Handle data URLs (base64 images) - return as is
    if (imagePath.startsWith('data:')) return imagePath;

    // Handle blob URLs - return as is
    if (imagePath.startsWith('blob:')) return imagePath;

    // Handle full HTTP URLs - return as is
    if (imagePath.startsWith('http')) return imagePath;

    // Clean up path - remove multiple slashes but preserve protocol slashes
    let cleanPath = imagePath.replace(/\/+/g, '/');

    // Ensure path starts with single slash
    if (!cleanPath.startsWith('/')) {
      cleanPath = '/' + cleanPath;
    }

    // Construct URL and clean up any double slashes in the result
    let finalUrl = `${API_URL}${cleanPath}`;

    // Fix double slashes after protocol (but preserve protocol://)
    finalUrl = finalUrl.replace(/([^:])\/+/g, '$1/');

    // Additional cleanup: ensure no double slashes anywhere
    finalUrl = finalUrl.replace(/\/\/+/g, '/').replace('http:/', 'http://').replace('https:/', 'https://');

    return finalUrl;
  };

  const imageUrl = getImageUrl(src);

  if (!imageUrl) {
    return (
      <div className={`bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`}>
        Không có hình ảnh
      </div>
    );
  }

  return (
    <img
      src={imageUrl}
      alt={alt}
      className={className}
      onError={(e) => {
        const target = e.target as HTMLImageElement;
        target.style.display = 'none';

        // Create placeholder if not exists
        if (!target.nextElementSibling?.classList.contains('error-placeholder')) {
          const placeholder = document.createElement('div');
          placeholder.className = `error-placeholder bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`;
          placeholder.textContent = 'Hình ảnh không tải được';
          target.parentNode?.appendChild(placeholder);
        }
      }}
    />
  );
};

export default SafeImage;
