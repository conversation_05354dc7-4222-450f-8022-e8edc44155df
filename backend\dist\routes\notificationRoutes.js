"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const notificationController_1 = require("../controllers/notificationController");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = express_1.default.Router();
// Get unread notifications count (no auth required for polling)
router.get('/unread-count', auth_middleware_1.verifyToken, notificationController_1.getUnreadCount);
// Apply protect middleware to routes that need authentication
// Get user's notifications
router.get('/', auth_middleware_1.verifyToken, notificationController_1.getUserNotifications);
// Mark notification as read
router.patch('/:notificationId/read', auth_middleware_1.verifyToken, notificationController_1.markAsRead);
// Mark all notifications as read
router.patch('/read-all', auth_middleware_1.verifyToken, notificationController_1.markAllAsRead);
// Admin routes
router.post('/admin/create', auth_middleware_1.verifyToken, notificationController_1.createAdminNotification);
router.get('/admin/stats', auth_middleware_1.verifyToken, notificationController_1.getNotificationStats);
router.get('/admin/all', auth_middleware_1.verifyToken, notificationController_1.getAllNotifications);
router.delete('/admin/:notificationId', auth_middleware_1.verifyToken, notificationController_1.deleteNotification);
// Test route (for development)
router.post('/test', auth_middleware_1.verifyToken, notificationController_1.createTestNotification);
exports.default = router;
