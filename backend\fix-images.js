const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Connect to MongoDB
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/keydyweb';
console.log('🔗 Using MongoDB URI:', MONGODB_URI ? 'Atlas URI configured' : 'Local MongoDB');

async function fixImages() {
  try {
    console.log('🔧 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get Campaign model
    const Campaign = mongoose.model('Campaign', new mongoose.Schema({
      title: String,
      images: [String]
    }, { collection: 'campaigns' }));

    // Get all campaigns
    const campaigns = await Campaign.find({});
    console.log(`📊 Found ${campaigns.length} campaigns to check`);

    // Get all files in uploads directory
    const uploadsDir = path.join(__dirname, 'uploads');
    const files = fs.readdirSync(uploadsDir).filter(file => 
      file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.gif')
    );
    console.log(`📁 Found ${files.length} image files in uploads:`, files);

    let fixedCount = 0;

    for (const campaign of campaigns) {
      console.log(`\n🔍 Checking campaign: ${campaign.title}`);
      console.log(`   Current images:`, campaign.images);

      let hasChanges = false;
      const newImages = [];

      // If campaign has no images, assign some default images
      if (campaign.images.length === 0) {
        // Assign first 3 available images as defaults
        const defaultImages = files.slice(0, 3).map(file => `/uploads/${file}`);
        newImages.push(...defaultImages);
        hasChanges = true;
        console.log(`   🎨 Added default images for empty campaign`);
      } else {
        // Process existing images
        for (let i = 0; i < campaign.images.length; i++) {
          const imagePath = campaign.images[i];
          console.log(`   Processing image ${i + 1}: ${imagePath}`);

          // Extract filename from path
          const filename = imagePath.split('/').pop() || imagePath;
          console.log(`   Extracted filename: ${filename}`);

          // Check if file exists in uploads
          if (files.includes(filename)) {
            newImages.push(`/uploads/${filename}`);
            console.log(`   ✅ Fixed: ${imagePath} -> /uploads/${filename}`);
            hasChanges = true;
          } else {
            // Map to available files in order
            const availableFile = files[i % files.length]; // Cycle through available files
            newImages.push(`/uploads/${availableFile}`);
            console.log(`   🔄 Mapped: ${imagePath} -> /uploads/${availableFile}`);
            hasChanges = true;
          }
        }
      }

      if (hasChanges) {
        campaign.images = newImages;
        await campaign.save();
        fixedCount++;
        console.log(`   ✅ Updated campaign: ${campaign.title}`);
        console.log(`   New images:`, newImages);
      } else {
        console.log(`   ⏭️ No changes needed for: ${campaign.title}`);
      }
    }

    console.log(`\n🎉 Fixed ${fixedCount} campaigns out of ${campaigns.length} total`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

fixImages();
