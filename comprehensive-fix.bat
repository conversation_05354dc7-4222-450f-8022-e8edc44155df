@echo off
echo ========================================
echo    COMPREHENSIVE IMAGE FIX
echo ========================================
echo.
echo This will completely fix all image issues
echo.
echo Step 1: Installing MongoDB driver...
npm install mongodb
echo.
echo Step 2: Debugging current state...
node debug-images.js
echo.
echo Step 3: Creating sample images if needed...
node create-sample-images.js
echo.
echo Step 4: Fixing campaign image references...
node fix-campaign-images.js
echo.
echo Step 5: Running additional cleanup...
node quick-fix-database.js
echo.
echo ========================================
echo    FIX COMPLETED SUCCESSFULLY
echo ========================================
echo.
echo Next steps:
echo 1. Restart frontend: cd frontend ^&^& npm run dev
echo 2. Test: http://localhost:5173/campaigns
echo 3. Test: http://localhost:5173/admin/campaigns
echo.
echo All image issues should now be resolved!
echo.
pause
