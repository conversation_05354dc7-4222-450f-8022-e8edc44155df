import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { format } from 'date-fns';
import api from '../../services/api';
import { FaEdit, FaTrash, FaEye } from 'react-icons/fa';
import { Campaign } from './types';
import UpdateCampaign from './UpdateCampaign';
import { useNavigate } from 'react-router-dom';
import SafeImage from '../../components/ui/SafeImage';
import SimpleImageTest from '../../components/SimpleImageTest';

const API_URL = import.meta.env.VITE_API_URL || '';

const ManageCampaigns: React.FC = () => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showDonors, setShowDonors] = useState(false);
  const [formData, setFormData] = useState<{
    title: string;
    description: string;
    targetAmount: string;
    startDate: string;
    endDate: string;
    category: string;
    images: string[];
    status: 'active' | 'completed' | 'cancelled';
  }>({
    title: '',
    description: '',
    targetAmount: '',
    startDate: '',
    endDate: '',
    category: '',
    images: [],
    status: 'active'
  });
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    fetchCampaigns();
  }, []);

  useEffect(() => {
    console.log('campaigns:', campaigns);
  }, [campaigns]);

  const fetchCampaigns = async () => {
    try {
      setLoading(true);
      // Use admin endpoint for campaign management
      const response = await api.get('/api/campaigns/admin');

      if (response.data && Array.isArray(response.data.campaigns)) {
        setCampaigns(response.data.campaigns);
      } else if (response.data && Array.isArray(response.data.data)) {
        // Handle different response structure
        setCampaigns(response.data.data);
      } else {
        setCampaigns([]);
        toast.error('Dữ liệu chiến dịch không hợp lệ!');
      }
    } catch (error: any) {
      console.error('Lỗi khi tải danh sách chiến dịch:', error);
      setCampaigns([]);
      if (error.response?.status === 401) {
        toast.error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại');
        localStorage.removeItem('token');
        navigate('/login');
      } else {
        toast.error(error.response?.data?.message || 'Có lỗi xảy ra khi tải danh sách chiến dịch');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setSelectedImages(prev => [...prev, ...files]);

      // Tạo URL preview cho các hình ảnh mới
      const newPreviewUrls = files.map(file => URL.createObjectURL(file));
      setImagePreviewUrls(prev => [...prev, ...newPreviewUrls]);
    }
  };

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviewUrls(prev => {
      const newUrls = [...prev];
      URL.revokeObjectURL(newUrls[index]);
      return newUrls.filter((_, i) => i !== index);
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Kiểm tra ngày kết thúc phải sau ngày bắt đầu
    const startDate = new Date(formData.startDate);
    const endDate = new Date(formData.endDate);
    if (endDate <= startDate) {
      toast.error('Ngày kết thúc phải sau ngày bắt đầu');
      return;
    }

    try {
      const formDataToSend = new FormData();
      formDataToSend.append('title', formData.title);
      formDataToSend.append('description', formData.description);
      formDataToSend.append('targetAmount', formData.targetAmount);
      formDataToSend.append('startDate', formData.startDate);
      formDataToSend.append('endDate', formData.endDate);
      formDataToSend.append('category', formData.category || 'Khác');
      formDataToSend.append('status', formData.status);

      // Thêm tất cả hình ảnh vào FormData
      selectedImages.forEach(image => {
        formDataToSend.append('images', image);
      });

      // Use api service which handles authentication automatically
      await api.post('/api/campaigns/admin', formDataToSend, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      toast.success('Chiến dịch đã được tạo thành công');
      setIsModalOpen(false);
      setFormData({
        title: '',
        description: '',
        targetAmount: '',
        startDate: '',
        endDate: '',
        category: '',
        images: [],
        status: 'active'
      });
      setSelectedImages([]);
      setImagePreviewUrls([]);
      fetchCampaigns();
    } catch (error) {
      console.error('Lỗi khi lưu chiến dịch:', error);
      toast.error('Không thể lưu chiến dịch');
    }
  };

  const handleEdit = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setIsModalOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa chiến dịch này?')) {
      try {
        await api.delete(`/api/campaigns/admin/${id}`);
        toast.success('Xóa chiến dịch thành công');
        fetchCampaigns();
      } catch (error) {
        console.error('Lỗi khi xóa chiến dịch:', error);
        toast.error('Không thể xóa chiến dịch');
      }
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  if (loading) {
    return <div className="loading">Đang tải...</div>;
  }

  return (
    <div className="manage-campaigns" style={{background:'#f6f8fa',minHeight:'100vh',padding:'32px 0'}}>
      {/* Temporary Simple Image Test */}
      <div className="p-4">
        <SimpleImageTest />
      </div>

      <div className="manage-campaigns-header" style={{display:'flex',alignItems:'center',justifyContent:'space-between',marginBottom:32,padding:'0 32px'}}>
        <h1 style={{fontWeight:800,fontSize:36,letterSpacing:1.2,color:'#222',margin:0}}>Quản lý Chiến dịch</h1>
        <button className="create-button" style={{background:'linear-gradient(45deg,#27ae60,#8A2BE2)',color:'#fff',fontSize:32,borderRadius:'50%',width:56,height:56,display:'flex',alignItems:'center',justifyContent:'center',boxShadow:'0 4px 16px rgba(39,174,96,0.12)',border:'none',cursor:'pointer',transition:'background 0.2s'}} onClick={() => setIsModalOpen(true)}>
          +
        </button>
      </div>
      <div className="campaigns-table-wrapper" style={{overflowX:'auto',background:'#fff',borderRadius:18,boxShadow:'0 4px 24px rgba(138,43,226,0.08)',padding:32,maxWidth:1200,margin:'0 auto'}}>
        <table className="campaigns-table" style={{width:'100%',borderCollapse:'separate',borderSpacing:0,fontSize:17,fontFamily:'Segoe UI',background:'#fff'}}>
          <thead>
            <tr style={{background:'linear-gradient(90deg,#f8f9fa 60%,#eaf1fa 100%)'}}>
              <th style={{padding:'16px 10px',textAlign:'center',fontWeight:700,color:'#8A2BE2',fontSize:18}}>Hình ảnh</th>
              <th style={{padding:'16px 10px',textAlign:'center',fontWeight:700,color:'#8A2BE2',fontSize:18}}>Tên chiến dịch</th>
              <th style={{padding:'16px 10px',textAlign:'center',fontWeight:700,color:'#8A2BE2',fontSize:18}}>Mã</th>
              <th style={{padding:'16px 10px',textAlign:'center',fontWeight:700,color:'#8A2BE2',fontSize:18}}>Mục tiêu</th>
              <th style={{padding:'16px 10px',textAlign:'center',fontWeight:700,color:'#8A2BE2',fontSize:18}}>Đã nhận</th>
              <th style={{padding:'16px 10px',textAlign:'center',fontWeight:700,color:'#8A2BE2',fontSize:18}}>Trạng thái</th>
              <th style={{padding:'16px 10px',textAlign:'center',fontWeight:700,color:'#8A2BE2',fontSize:18}}>Ngày bắt đầu</th>
              <th style={{padding:'16px 10px',textAlign:'center',fontWeight:700,color:'#8A2BE2',fontSize:18}}>Ngày kết thúc</th>
              <th style={{padding:'16px 10px',textAlign:'center',fontWeight:700,color:'#8A2BE2',fontSize:18}}>Hành động</th>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(campaigns) && campaigns.length > 0 ? (
              campaigns.map((campaign, i) => (
                <tr key={campaign._id} style={{borderBottom:'1px solid #eee',transition:'background 0.2s',background:i%2===0?'#fcfcff':'#f8f9fa',cursor:'pointer'}}>
                  <td style={{padding:'10px',textAlign:'center'}}>
                    {campaign.images && campaign.images.length > 0 && (
                      <SafeImage
                        src={campaign.images[0]}
                        alt="Ảnh chiến dịch"
                        className="w-15 h-10 object-cover rounded"
                      />
                    )}
                  </td>
                  <td style={{maxWidth:200,overflow:'hidden',textOverflow:'ellipsis',whiteSpace:'nowrap',padding:'14px 10px',fontWeight:600,fontSize:17}}>{campaign.title}</td>
                  <td style={{fontFamily:'monospace',fontSize:14,padding:'14px 10px',textAlign:'center',color:'#888'}}>{campaign.campaignId}</td>
                  <td style={{padding:'14px 10px',textAlign:'right',color:'#8A2BE2',fontWeight:600}}>{formatCurrency(campaign.targetAmount)}</td>
                  <td style={{padding:'14px 10px',textAlign:'right',fontWeight:700,color:'#27ae60'}}>{formatCurrency(campaign.currentAmount)}</td>
                  <td style={{padding:'14px 10px',textAlign:'center'}}>
                    <span className={`status-badge ${campaign.status}`} style={{
                      display:'inline-block',
                      minWidth:110,
                      padding:'6px 18px',
                      borderRadius:20,
                      fontWeight:700,
                      background: campaign.status==='active' ? 'linear-gradient(90deg,#eafaf1,#d4f5e9)' : campaign.status==='completed' ? 'linear-gradient(90deg,#eaf1fa,#d4e6fa)' : 'linear-gradient(90deg,#faeaea,#f9dada)',
                      color: campaign.status==='active' ? '#27ae60' : campaign.status==='completed' ? '#2980b9' : '#e74c3c',
                      boxShadow:'0 1px 4px rgba(0,0,0,0.04)',
                      fontSize:16
                    }}>{campaign.status === 'active' ? 'Đang diễn ra' : campaign.status === 'completed' ? 'Đã hoàn thành' : 'Đã hủy'}</span>
                  </td>
                  <td style={{padding:'14px 10px',textAlign:'center',fontSize:15}}>{format(new Date(campaign.startDate), 'dd/MM/yyyy')}</td>
                  <td style={{padding:'14px 10px',textAlign:'center',fontSize:15}}>{format(new Date(campaign.endDate), 'dd/MM/yyyy')}</td>
                  <td style={{display:'flex',gap:10,justifyContent:'center',alignItems:'center',padding:'14px 10px'}}>
                    <button className="icon-btn" style={{background:'#fff',border:'1px solid #eee',borderRadius:'50%',padding:10,cursor:'pointer',transition:'box-shadow 0.2s',boxShadow:'0 2px 8px rgba(138,43,226,0.07)'}} onClick={() => handleEdit(campaign)} title="Chỉnh sửa"><FaEdit color="#2980b9" size={18} /></button>
                    <button className="icon-btn" style={{background:'#fff',border:'1px solid #eee',borderRadius:'50%',padding:10,cursor:'pointer',transition:'box-shadow 0.2s',boxShadow:'0 2px 8px rgba(231,76,60,0.07)'}} onClick={() => handleDelete(campaign._id)} title="Xóa"><FaTrash color="#e74c3c" size={18} /></button>
                    <button className="icon-btn" style={{background:'#fff',border:'1px solid #eee',borderRadius:'50%',padding:10,cursor:'pointer',transition:'box-shadow 0.2s',boxShadow:'0 2px 8px rgba(39,174,96,0.07)'}} onClick={() => { setSelectedCampaign(campaign); setShowDonors(true); }} title="Xem người quyên góp"><FaEye color="#27ae60" size={18} /></button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={9} style={{textAlign:'center',padding:'32px 0',color:'#888',fontSize:18}}>
                  Không có chiến dịch nào để hiển thị.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {isModalOpen && !selectedCampaign && (
        <div className="modal">
          <div className="modal-content">
            <h2>Tạo Chiến dịch Mới</h2>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="title">Tên chiến dịch:</label>
                <input
                  id="title"
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="description">Mô tả:</label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="targetAmount">Số tiền mục tiêu (VNĐ):</label>
                <input
                  id="targetAmount"
                  type="number"
                  name="targetAmount"
                  value={formData.targetAmount}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="category">Danh mục:</label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                >
                  <option value="">Chọn danh mục</option>
                  <option value="Giáo dục">Giáo dục</option>
                  <option value="Y tế">Y tế</option>
                  <option value="Môi trường">Môi trường</option>
                  <option value="Trẻ em">Trẻ em</option>
                  <option value="Người già">Người già</option>
                  <option value="Động vật">Động vật</option>
                  <option value="Cộng đồng">Cộng đồng</option>
                  <option value="Khác">Khác</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="startDate">Ngày bắt đầu:</label>
                <input
                  id="startDate"
                  type="date"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="endDate">Ngày kết thúc:</label>
                <input
                  id="endDate"
                  type="date"
                  name="endDate"
                  value={formData.endDate}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="images">Hình ảnh:</label>
                <input
                  id="images"
                  type="file"
                  name="images"
                  accept="image/*"
                  multiple
                  onChange={handleImageChange}
                />
                <div className="image-preview-grid">
                  {imagePreviewUrls.map((url, index) => (
                    <div key={index} className="image-preview">
                      <img src={url} alt={`Preview ${index + 1}`} />
                      <button
                        type="button"
                        className="remove-image"
                        onClick={() => removeImage(index)}
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
              <div className="form-group">
                <label htmlFor="status">Trạng thái:</label>
                <select
                  id="status"
                  value={formData.status}
                  name="status"
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as 'active' | 'completed' | 'cancelled' })}
                >
                  <option value="active">Đang hoạt động</option>
                  <option value="completed">Đã hoàn thành</option>
                  <option value="cancelled">Đã hủy</option>
                </select>
              </div>
              <div className="modal-actions">
                <button type="submit">Tạo mới</button>
                <button type="button" onClick={() => {
                  setIsModalOpen(false);
                  setFormData({
                    title: '',
                    description: '',
                    targetAmount: '',
                    startDate: '',
                    endDate: '',
                    category: '',
                    images: [],
                    status: 'active'
                  });
                }}>
                  Hủy
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {isModalOpen && selectedCampaign && (
        <UpdateCampaign
          campaign={selectedCampaign}
          onUpdate={fetchCampaigns}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedCampaign(null);
          }}
        />
      )}

      {showDonors && selectedCampaign && (
        <div className="modal">
          <div className="modal-content">
            <h2>Danh sách người quyên góp</h2>
            <div className="donors-list">
              {selectedCampaign.donors.map(donor => (
                <div key={donor._id} className="donor-item">
                  <div className="donor-info">
                    <p className="donor-name">
                      {donor.isAnonymous ? 'Người quyên góp ẩn danh' : donor.name}
                    </p>
                    <p className="donor-transaction">Mã giao dịch: {donor.transactionId}</p>
                  </div>
                  <div className="donor-details">
                    <p className="donor-amount">{formatCurrency(donor.amount)}</p>
                    <p className="donor-date">
                      {format(new Date(donor.createdAt), 'dd/MM/yyyy HH:mm')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="modal-actions">
              <button onClick={() => {
                setShowDonors(false);
                setSelectedCampaign(null);
              }}>
                Đóng
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ManageCampaigns;