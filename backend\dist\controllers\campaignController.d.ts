import { Request, Response } from 'express';
export declare const createCampaign: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getCampaigns: (req: Request, res: Response) => Promise<void>;
export declare const getPublicCampaigns: (req: Request, res: Response) => Promise<void>;
export declare const getCampaignById: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateCampaign: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteCampaign: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateCampaignStatus: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const manualUpdateCampaignStatuses: (req: Request, res: Response) => Promise<void>;
export declare const fixCampaignImages: (req: Request, res: Response) => Promise<void>;
export declare const createCampaignExport: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getCampaignsExport: (req: Request, res: Response) => Promise<void>;
export declare const updateCampaignExport: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteCampaignExport: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateCampaignStatusExport: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getPublicCampaignsExport: (req: Request, res: Response) => Promise<void>;
export declare const getCampaignByIdExport: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=campaignController.d.ts.map