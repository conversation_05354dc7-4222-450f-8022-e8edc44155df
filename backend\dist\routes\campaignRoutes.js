"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const campaignController_1 = require("../controllers/campaignController");
const upload_1 = __importDefault(require("../middleware/upload"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = express_1.default.Router();
console.log('🔧 [Campaign] Campaign routes loaded');
// Test route
router.get('/admin/test', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, (req, res) => {
    var _a;
    console.log('🧪 [Campaign] Test route called');
    res.json({ success: true, message: 'Campaign admin test route working', user: (_a = req.user) === null || _a === void 0 ? void 0 : _a.name });
});
// Public fix images route (for testing)
router.get('/fix-images', campaignController_1.fixCampaignImages);
// Admin routes
router.post('/admin/simple', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, campaignController_1.createCampaign); // Simple route without file upload
router.post('/admin', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, upload_1.default.array('images', 5), campaignController_1.createCampaign);
router.get('/admin', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, campaignController_1.getCampaigns);
router.put('/admin/:id', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, upload_1.default.array('images', 5), campaignController_1.updateCampaign);
router.delete('/admin/:id', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, campaignController_1.deleteCampaign);
router.patch('/admin/:id/status', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, campaignController_1.updateCampaignStatus);
router.post('/admin/update-statuses', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, campaignController_1.manualUpdateCampaignStatuses);
router.post('/admin/fix-images', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, campaignController_1.fixCampaignImages);
// Public routes
router.get('/', campaignController_1.getPublicCampaigns);
router.get('/:id', campaignController_1.getCampaignById);
exports.default = router;
