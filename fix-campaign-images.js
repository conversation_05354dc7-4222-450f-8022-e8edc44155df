const fs = require('fs');
const path = require('path');
const { MongoClient } = require('mongodb');

// MongoDB URI
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://keydyweb:<EMAIL>/keydyweb?retryWrites=true&w=majority';

async function fixCampaignImages() {
  let client;
  
  try {
    console.log('🔧 FIXING CAMPAIGN IMAGES...\n');

    // 1. Get available files
    const uploadsDir = path.join(__dirname, 'backend/uploads');
    if (!fs.existsSync(uploadsDir)) {
      console.log('❌ Uploads directory does not exist!');
      return;
    }

    const availableFiles = fs.readdirSync(uploadsDir).filter(file =>
      file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.gif') || file.endsWith('.jpeg')
    );
    
    console.log(`📁 Found ${availableFiles.length} available files:`);
    availableFiles.forEach(file => console.log(`   - ${file}`));

    // 2. Connect to database
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    console.log('\n✅ Connected to MongoDB');

    const db = client.db();
    const campaigns = await db.collection('campaigns').find({}).toArray();
    console.log(`📊 Found ${campaigns.length} campaigns`);

    let fixedCount = 0;
    let removedCount = 0;

    // 3. Fix each campaign
    for (const campaign of campaigns) {
      console.log(`\n🔍 Processing: ${campaign.title}`);
      
      if (!campaign.images || !Array.isArray(campaign.images)) {
        console.log('   ⚠️ No images array, skipping...');
        continue;
      }

      const originalImages = [...campaign.images];
      const fixedImages = [];

      for (const imagePath of originalImages) {
        console.log(`   📷 Checking: ${imagePath}`);
        
        // Skip data URLs and blob URLs
        if (imagePath.startsWith('data:') || imagePath.startsWith('blob:')) {
          console.log('      ✅ Data/Blob URL, keeping as is');
          fixedImages.push(imagePath);
          continue;
        }

        // Extract filename
        let filename = '';
        if (imagePath.startsWith('/uploads/')) {
          filename = imagePath.replace('/uploads/', '');
        } else if (imagePath.startsWith('uploads/')) {
          filename = imagePath.replace('uploads/', '');
        } else if (imagePath.startsWith('http')) {
          // Extract filename from full URL
          const urlParts = imagePath.split('/');
          filename = urlParts[urlParts.length - 1];
        } else {
          filename = imagePath;
        }

        // Check if file exists
        if (availableFiles.includes(filename)) {
          console.log(`      ✅ File exists: ${filename}`);
          fixedImages.push(filename); // Store as simple filename
          continue;
        }

        // Try to find similar file
        console.log(`      ❌ File missing: ${filename}`);
        
        // Extract timestamp and base name for matching
        const parts = filename.split('-');
        if (parts.length >= 2) {
          const baseName = parts[0];
          const timestamp = parts[1];
          
          // Find files with same timestamp
          const similarFiles = availableFiles.filter(file => {
            return file.includes(timestamp) && file.includes(baseName);
          });

          if (similarFiles.length > 0) {
            console.log(`      🔄 Found similar file: ${similarFiles[0]}`);
            fixedImages.push(similarFiles[0]);
            continue;
          }

          // Find files with same base name
          const baseNameFiles = availableFiles.filter(file => {
            return file.startsWith(baseName + '-');
          });

          if (baseNameFiles.length > 0) {
            console.log(`      🔄 Found base name match: ${baseNameFiles[0]}`);
            fixedImages.push(baseNameFiles[0]);
            continue;
          }
        }

        console.log(`      🗑️ No match found, removing reference`);
        removedCount++;
      }

      // Update campaign if changes were made
      if (JSON.stringify(fixedImages) !== JSON.stringify(originalImages)) {
        console.log(`   📝 Updating campaign images:`);
        console.log(`      Before: ${originalImages.length} images`);
        console.log(`      After:  ${fixedImages.length} images`);

        await db.collection('campaigns').updateOne(
          { _id: campaign._id },
          { $set: { images: fixedImages } }
        );

        fixedCount++;
        console.log(`   ✅ Campaign updated`);
      } else {
        console.log(`   ✅ No changes needed`);
      }
    }

    console.log(`\n🎉 FIXING COMPLETE:`);
    console.log(`   - Campaigns processed: ${campaigns.length}`);
    console.log(`   - Campaigns fixed: ${fixedCount}`);
    console.log(`   - Image references removed: ${removedCount}`);

    // 4. Add sample images if no campaigns have images
    const campaignsWithImages = await db.collection('campaigns').countDocuments({
      images: { $exists: true, $ne: [], $not: { $size: 0 } }
    });

    if (campaignsWithImages === 0 && availableFiles.length > 0) {
      console.log(`\n📷 No campaigns have images. Adding sample images...`);
      
      const campaignsToUpdate = await db.collection('campaigns').find({}).limit(3).toArray();
      
      for (let i = 0; i < campaignsToUpdate.length && i < availableFiles.length; i++) {
        const campaign = campaignsToUpdate[i];
        const sampleImage = availableFiles[i % availableFiles.length];
        
        await db.collection('campaigns').updateOne(
          { _id: campaign._id },
          { $set: { images: [sampleImage] } }
        );
        
        console.log(`   ✅ Added ${sampleImage} to "${campaign.title}"`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (client) {
      await client.close();
      console.log('\n🔌 Disconnected from MongoDB');
    }
  }
}

// Check if mongodb driver is available
try {
  require('mongodb');
  fixCampaignImages();
} catch (error) {
  console.log('❌ MongoDB driver not found. Installing...');
  console.log('Please run: npm install mongodb');
  console.log('Then run this script again.');
}
