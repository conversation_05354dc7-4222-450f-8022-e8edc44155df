const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const http = require('http');
require('dotenv').config();

// Connect to MongoDB
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/keydyweb';

function testImageUrl(url) {
  return new Promise((resolve) => {
    const req = http.get(url, (res) => {
      resolve(res.statusCode === 200);
    });
    req.on('error', () => {
      resolve(false);
    });
    req.setTimeout(5000, () => {
      req.destroy();
      resolve(false);
    });
  });
}

async function verifyImages() {
  try {
    console.log('🔧 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get Campaign model
    const Campaign = mongoose.model('Campaign', new mongoose.Schema({
      title: String,
      images: [String]
    }, { collection: 'campaigns' }));

    // Get all campaigns
    const campaigns = await Campaign.find({});
    console.log(`📊 Found ${campaigns.length} campaigns to verify`);

    // Get all files in uploads directory
    const uploadsDir = path.join(__dirname, 'uploads');
    const files = fs.readdirSync(uploadsDir).filter(file => 
      file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.gif')
    );
    console.log(`📁 Found ${files.length} image files in uploads:`, files);

    for (const campaign of campaigns) {
      console.log(`\n🔍 Verifying campaign: ${campaign.title}`);
      console.log(`   Images:`, campaign.images);
      
      for (let i = 0; i < campaign.images.length; i++) {
        const imagePath = campaign.images[i];
        const fullUrl = `http://localhost:5001${imagePath}`;
        
        console.log(`   Testing image ${i + 1}: ${fullUrl}`);
        
        const isAccessible = await testImageUrl(fullUrl);
        if (isAccessible) {
          console.log(`   ✅ Image ${i + 1} is accessible`);
        } else {
          console.log(`   ❌ Image ${i + 1} is NOT accessible`);
        }
      }
    }

    console.log(`\n🎉 Verification completed`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

verifyImages();
