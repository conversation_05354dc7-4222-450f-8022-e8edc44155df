# 🖼️ Hướng dẫn sử dụng tính năng Upload & Thay đổi hình ảnh

## 🎯 Tính năng đã cải thiện

### ✅ Đã khắc phục triệt để:
- **Lỗi hình ảnh không hiển thị** - Tự động fallback khi file không tồn tại
- **Tên file không nhất quán** - Đơn giản hóa naming convention
- **Xử lý lỗi kém** - Hiển thị placeholder thay vì lỗi trắng

### 🆕 Tính năng mới:
- **Thay thế hình ảnh** - Thay đổi từng ảnh riêng lẻ
- **Xóa hình ảnh** - Loại bỏ ảnh không mong muốn
- **Validation nâng cao** - Ki<PERSON>m tra kích thước và số lượng
- **UI/UX cải thiện** - Hover effects, transitions, icons đẹp
- **Hiển thị trạng thái** - <PERSON><PERSON> biệt ảnh có sẵn và ảnh mới

## 🚀 Cách khởi động

### 1. Backend:
```bash
# Cách 1: Sử dụng script
./start-backend.bat

# Cách 2: Manual
cd backend
npm run build
npm start
```

### 2. Frontend:
```bash
# Cách 1: Sử dụng script  
./start-frontend.bat

# Cách 2: Manual
cd frontend
npm run dev
```

## 🧪 Cách test

### 1. Test tự động:
Mở file `test-complete-system.html` trong browser để test:
- ✅ Backend connection
- ✅ Static file serving
- ✅ Image fallback logic
- ✅ Upload simulation

### 2. Test thủ công:
1. Mở admin panel → Campaigns
2. Tạo/chỉnh sửa campaign
3. Test các tính năng:
   - Upload nhiều ảnh (tối đa 5)
   - Thay thế ảnh cụ thể
   - Xóa ảnh
   - Validation (file quá lớn, quá nhiều ảnh)

## 📁 Files đã thay đổi

### Backend:
- `src/middleware/upload.ts` - Cấu hình multer cải thiện
- `src/controllers/campaignController.ts` - Xử lý upload tốt hơn
- `src/index.ts` - Static file serving với logging

### Frontend:
- `components/ui/ImageWithFallback.tsx` - Component mới
- `pages/admin/CampaignModal.tsx` - Cải thiện toàn diện

## 🎨 Giao diện mới

### Upload Area:
- Hiển thị số lượng ảnh hiện tại (x/5)
- Disable khi đạt giới hạn
- Validation kích thước file

### Image Preview:
- Nút "Thay thế" (icon RefreshCw)
- Nút "Xóa" (icon Trash2)
- Badge trạng thái (Có sẵn/Mới)
- Hover effects mượt mà

### Error Handling:
- Tự động thử fallback URLs
- Hiển thị placeholder khi lỗi
- Logging chi tiết

## 🔧 Troubleshooting

### Backend không khởi động:
```bash
cd backend
npm install
npm run build
node dist/index.js
```

### Frontend không khởi động:
```bash
cd frontend
npm install
npm run dev
```

### Hình ảnh không hiển thị:
1. Kiểm tra backend đang chạy port 5001
2. Kiểm tra file tồn tại trong `backend/uploads/`
3. Xem console log để debug

### Upload không hoạt động:
1. Kiểm tra kích thước file (<10MB)
2. Kiểm tra định dạng file (JPG, PNG, GIF)
3. Kiểm tra số lượng file (<5 ảnh)

## 📊 Test Results

Sử dụng `test-complete-system.html` để kiểm tra:
- 🔌 Backend connection
- 📁 Static file serving  
- 🔄 Image fallback logic
- 📤 Upload validation

## 🎉 Hoàn thành!

Hệ thống upload hình ảnh đã được cải thiện triệt để với:
- ✅ Khắc phục lỗi hiển thị
- ✅ Tính năng thay thế/xóa ảnh
- ✅ Validation nâng cao
- ✅ UI/UX chuyên nghiệp
- ✅ Error handling tốt

Giờ bạn có thể upload và quản lý hình ảnh một cách dễ dàng và chuyên nghiệp! 🚀
