import React from 'react';

const ImageTest: React.FC = () => {
  const testUrls = [
    'http://localhost:5001/uploads/683eb72fd25e6013197bc878/images-1747917385255.jpg',
    '/uploads/683eb72fd25e6013197bc878/images-1747917385255.jpg',
    'uploads/683eb72fd25e6013197bc878/images-1747917385255.jpg'
  ];

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Image Test</h2>
      
      {testUrls.map((url, index) => (
        <div key={index} className="mb-4 p-4 border rounded">
          <h3 className="font-semibold mb-2">Test {index + 1}: {url}</h3>
          <img 
            src={url} 
            alt={`Test ${index + 1}`}
            className="w-32 h-32 object-cover border"
            onLoad={() => console.log(`✅ Image ${index + 1} loaded:`, url)}
            onError={(e) => console.error(`❌ Image ${index + 1} failed:`, url, e)}
          />
        </div>
      ))}
    </div>
  );
};

export default ImageTest;
