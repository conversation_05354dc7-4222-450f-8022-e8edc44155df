"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.io = void 0;
// Load environment variables FIRST
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const mongoose_1 = __importDefault(require("mongoose"));
const passport_1 = __importDefault(require("passport"));
// Import type definitions
require("./types/express");
const auth_routes_1 = __importDefault(require("./routes/auth.routes"));
const user_routes_1 = __importDefault(require("./routes/user.routes"));
// import charityRoutes from './routes/charity.routes';
const donationRoutes_1 = __importDefault(require("./routes/donationRoutes"));
const home_routes_1 = __importDefault(require("./routes/home.routes"));
const campaignRoutes_1 = __importDefault(require("./routes/campaignRoutes"));
const test_routes_1 = __importDefault(require("./routes/test.routes"));
console.log('🔧 [Server] Campaign routes imported:', typeof campaignRoutes_1.default);
const http_1 = __importDefault(require("http"));
const socket_io_1 = require("socket.io");
const posts_routes_1 = __importDefault(require("./routes/posts.routes"));
const notificationRoutes_1 = __importDefault(require("./routes/notificationRoutes"));
const admin_routes_1 = __importDefault(require("./routes/admin.routes"));
const event_routes_1 = __importDefault(require("./routes/event.routes"));
const badgeRoutes_1 = __importDefault(require("./routes/badgeRoutes"));
// Import cron jobs
const updateCampaignStatus_1 = require("./cron/updateCampaignStatus");
const cleanupDonations_1 = require("./cron/cleanupDonations");
const campaignReminders_1 = require("./cron/campaignReminders");
const eventReminders_1 = require("./cron/eventReminders");
const activity_tracker_1 = require("./utils/activity-tracker");
// Create Express app
const app = (0, express_1.default)();
// Create HTTP server with increased header size limit
const server = http_1.default.createServer({
    maxHeaderSize: 16384 // 16KB header limit (default is 8KB)
}, app);
// Create Socket.IO server
exports.io = new socket_io_1.Server(server, {
    cors: {
        origin: ['http://localhost:5173', 'http://*********.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174'],
        methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
        credentials: true
    }
});
// Socket.IO connection handling
exports.io.on('connection', (socket) => {
    console.log('User connected:', socket.id);
    // Join user to their own room for personal notifications
    socket.on('join-user', (userId) => {
        if (userId) {
            socket.join(`user-${userId}`);
            console.log(`User ${userId} joined their room`);
            // Send confirmation
            socket.emit('joined-room', { userId, room: `user-${userId}` });
        }
    });
    // Handle authentication with token
    socket.on('authenticate', (token) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            // Verify JWT token and extract user ID
            const jwt = yield Promise.resolve().then(() => __importStar(require('jsonwebtoken')));
            const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret');
            if (decoded && decoded._id) {
                socket.join(`user-${decoded._id}`);
                console.log(`Authenticated user ${decoded._id} joined room: user-${decoded._id}`);
                socket.emit('authenticated', { userId: decoded._id });
            }
        }
        catch (error) {
            console.error('Socket authentication failed:', error);
            socket.emit('auth-error', { message: 'Authentication failed' });
        }
    }));
    // Handle disconnect
    socket.on('disconnect', () => {
        console.log('User disconnected:', socket.id);
    });
});
// Middleware
app.use(express_1.default.json({ limit: '50mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '50mb' }));
// Multer is configured in individual routes where needed
// CORS configuration
app.use((0, cors_1.default)({
    origin: [
        'http://localhost:5173', 'http://127.0.0.1:5173',
        'http://localhost:5174', 'http://127.0.0.1:5174',
        'http://localhost:5175', 'http://127.0.0.1:5175',
        'http://localhost:3000', 'http://127.0.0.1:3000'
    ],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
    optionsSuccessStatus: 200
}));
// Handle preflight requests explicitly
app.options('*', (req, res) => {
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.sendStatus(200);
});
// Initialize Passport
app.use(passport_1.default.initialize());
// Middleware to attach Socket.IO to requests
app.use((req, _res, next) => {
    req.io = exports.io;
    next();
});
// Logging middleware
app.use((req, _res, next) => {
    console.log(`🌐 ${new Date().toISOString()} - ${req.method} ${req.url}`);
    // Log header sizes to debug 431 errors
    const headerSize = JSON.stringify(req.headers).length;
    if (headerSize > 4000) { // Log if headers > 4KB
        console.warn(`⚠️ Large headers detected: ${headerSize} bytes`);
        console.log('📋 Headers:', Object.keys(req.headers));
        if (req.headers.authorization) {
            const authSize = req.headers.authorization.length;
            console.log(`🔑 Auth header size: ${authSize} bytes`);
        }
    }
    if (req.body && Object.keys(req.body).length > 0) {
        const bodySize = JSON.stringify(req.body).length;
        console.log(`📝 Request body size: ${bodySize} bytes`);
    }
    next();
});
// Routes
console.log('🔧 [Server] Mounting routes...');
app.use('/api/auth', auth_routes_1.default);
app.use('/api/users', user_routes_1.default);
app.use('/api/donations', donationRoutes_1.default);
app.use('/api/home', home_routes_1.default);
console.log('🔧 [Server] Mounting campaign routes...');
app.use('/api/campaigns', campaignRoutes_1.default);
app.use('/api/posts', posts_routes_1.default); // Posts routes with full functionality
app.use('/api/notifications', notificationRoutes_1.default); // Notification routes
app.use('/api/admin', admin_routes_1.default);
app.use('/api/events', event_routes_1.default); // Event routes
app.use('/api/badges', badgeRoutes_1.default); // Badge routes
app.use('/api/test', test_routes_1.default);
console.log('🔧 [Server] All routes mounted');
// Static folder for uploads
app.use('/uploads', express_1.default.static('uploads'));
// Error handling middleware
const errorHandler = (err, _req, res, _next) => {
    console.error(err.stack);
    res.status(err.status || 500).json({
        message: err.message || 'Internal Server Error',
        error: process.env.NODE_ENV === 'development' ? err : {}
    });
};
app.use(errorHandler);
// Database connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/keydyweb';
console.log('🚀 Starting server...');
console.log('📊 MongoDB URI:', MONGODB_URI ? 'Atlas URI configured' : 'Local MongoDB');
mongoose_1.default.connect(MONGODB_URI, {
    serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
    socketTimeoutMS: 45000,
})
    .then(() => {
    console.log('✅ Connected to MongoDB');
    // Start cron jobs
    console.log('⏰ Starting cron jobs...');
    updateCampaignStatus_1.updateCampaignStatusJob.start();
    cleanupDonations_1.cleanupPendingDonations.start();
    (0, campaignReminders_1.startCampaignReminderJob)();
    (0, eventReminders_1.startEventReminderJob)();
    console.log('✅ Cron jobs started');
    // Initialize activity tracking cleanup
    (0, activity_tracker_1.initActivityCleanup)();
    console.log('🟢 Activity tracking initialized');
    // Start server after DB connection
    const PORT = process.env.PORT || 5001;
    server.listen(PORT, () => {
        console.log(`🚀 Server is running on port ${PORT}`);
        console.log('🌐 CORS enabled for:', ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']);
        console.log('🔌 Socket.IO enabled');
        console.log('⏰ Cron jobs running:');
        console.log('  - Campaign status update (every hour)');
        console.log('  - Cleanup pending donations (every hour)');
        console.log('  - Campaign deadline reminders (daily at 9:00 AM)');
        console.log('  - Event reminders (hourly)');
        console.log('📋 Available routes:');
        console.log('  - /api/auth');
        console.log('  - /api/campaigns');
        console.log('  - /api/admin');
        console.log('  - /api/posts');
        console.log('  - /api/test');
    });
})
    .catch((error) => {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
});
// Handle unhandled promise rejections
process.on('unhandledRejection', (error) => {
    console.error('Unhandled Promise Rejection:', error);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
