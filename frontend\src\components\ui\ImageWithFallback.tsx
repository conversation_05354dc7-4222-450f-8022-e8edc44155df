import React, { useState, useEffect } from 'react';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface ImageWithFallbackProps {
  src: string;
  alt: string;
  className?: string;
  fallbackText?: string;
  onLoad?: () => void;
  onError?: () => void;
}

const ImageWithFallback: React.FC<ImageWithFallbackProps> = ({
  src,
  alt,
  className = '',
  fallbackText = 'Hình ảnh không tải được',
  onLoad,
  onError
}) => {
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [hasError, setHasError] = useState<boolean>(false);
  const [attemptCount, setAttemptCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Helper function to get correct image URL
  const getImageUrl = (imagePath: string): string => {
    if (!imagePath) return '';

    // If it's already a full URL (starts with http), return as is
    if (imagePath.startsWith('http')) {
      return imagePath;
    }

    // If it starts with /uploads, prepend API_URL
    if (imagePath.startsWith('/uploads')) {
      return `${API_URL}${imagePath}`;
    }

    // If it starts with uploads (without /), prepend API_URL with /
    if (imagePath.startsWith('uploads')) {
      return `${API_URL}/${imagePath}`;
    }

    // Default case: assume it's a filename only
    return `${API_URL}/uploads/${imagePath}`;
  };

  // Helper function to get fallback image URLs
  const getFallbackUrls = (originalUrl: string): string[] => {
    const fallbacks: string[] = [];

    if (originalUrl.includes('/uploads/')) {
      const filename = originalUrl.split('/').pop() || '';

      // If filename has UUID, try without UUID
      if (filename.includes('-') && filename.split('-').length > 2) {
        const parts = filename.split('-');
        const simpleFilename = `${parts[0]}-${parts[1]}.jpg`;
        const fallbackUrl = `${API_URL}/uploads/${simpleFilename}`;

        // Only add if different from original
        if (fallbackUrl !== originalUrl) {
          fallbacks.push(fallbackUrl);
        }
      }

      // Try with different extensions only if not already tried
      const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
      const pngUrl = `${API_URL}/uploads/${nameWithoutExt}.png`;
      const jpegUrl = `${API_URL}/uploads/${nameWithoutExt}.jpeg`;

      if (pngUrl !== originalUrl && !fallbacks.includes(pngUrl)) {
        fallbacks.push(pngUrl);
      }
      if (jpegUrl !== originalUrl && !fallbacks.includes(jpegUrl)) {
        fallbacks.push(jpegUrl);
      }
    }

    // Remove duplicates and limit to 2 fallbacks max
    return [...new Set(fallbacks)].slice(0, 2);
  };

  // Initialize current source
  useEffect(() => {
    if (src) {
      setCurrentSrc(getImageUrl(src));
      setHasError(false);
      setAttemptCount(0);
      setIsLoading(true);
    } else {
      setHasError(true);
      setIsLoading(false);
    }
  }, [src]);

  const handleImageError = () => {
    console.error('❌ [ImageWithFallback] Failed to load image:', currentSrc);

    // Prevent infinite loops - max 3 attempts
    if (attemptCount >= 3) {
      console.error('❌ [ImageWithFallback] Max attempts reached for:', src);
      setHasError(true);
      onError?.();
      return;
    }

    // Try fallback URLs
    const fallbackUrls = getFallbackUrls(currentSrc);

    if (attemptCount < fallbackUrls.length) {
      const fallbackUrl = fallbackUrls[attemptCount];

      // Avoid trying the same URL again
      if (fallbackUrl !== currentSrc) {
        console.log(`🔄 [ImageWithFallback] Trying fallback ${attemptCount + 1}:`, fallbackUrl);
        setAttemptCount(prev => prev + 1);
        setIsLoading(true);
        // Add small delay to prevent rapid fire requests
        setTimeout(() => {
          setCurrentSrc(fallbackUrl);
        }, 100);
        return;
      }
    }

    // All fallbacks failed or no more unique URLs
    console.error('❌ [ImageWithFallback] All fallback attempts failed for:', src);
    setHasError(true);
    onError?.();
  };

  const handleImageLoad = () => {
    console.log('✅ [ImageWithFallback] Image loaded successfully:', currentSrc);
    setHasError(false);
    setIsLoading(false);
    onLoad?.();
  };

  if (hasError) {
    return (
      <div className={`bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`}>
        {fallbackText}
      </div>
    );
  }

  return (
    <div className="relative">
      {isLoading && (
        <div className={`absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center ${className}`}>
          <div className="text-gray-400 text-xs">Đang tải...</div>
        </div>
      )}
      <img
        src={currentSrc}
        alt={alt}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
        onError={handleImageError}
        onLoad={handleImageLoad}
        style={{ display: hasError ? 'none' : 'block' }}
      />
    </div>
  );
};

export default ImageWithFallback;
