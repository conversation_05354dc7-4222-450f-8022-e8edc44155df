import React, { useState, useEffect } from 'react';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface ImageWithFallbackProps {
  src: string;
  alt: string;
  className?: string;
  fallbackText?: string;
  onLoad?: () => void;
  onError?: () => void;
}

const ImageWithFallback: React.FC<ImageWithFallbackProps> = ({
  src,
  alt,
  className = '',
  fallbackText = 'Hình ảnh không tải được',
  onLoad,
  onError
}) => {
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [hasError, setHasError] = useState<boolean>(false);
  const [attemptCount, setAttemptCount] = useState<number>(0);

  // Helper function to get correct image URL
  const getImageUrl = (imagePath: string): string => {
    if (!imagePath) return '';

    // If it's already a full URL (starts with http), return as is
    if (imagePath.startsWith('http')) {
      return imagePath;
    }

    // If it starts with /uploads, prepend API_URL
    if (imagePath.startsWith('/uploads')) {
      return `${API_URL}${imagePath}`;
    }

    // If it starts with uploads (without /), prepend API_URL with /
    if (imagePath.startsWith('uploads')) {
      return `${API_URL}/${imagePath}`;
    }

    // Default case: assume it's a filename only
    return `${API_URL}/uploads/${imagePath}`;
  };

  // Helper function to get fallback image URLs
  const getFallbackUrls = (originalUrl: string): string[] => {
    const fallbacks: string[] = [];
    
    if (originalUrl.includes('/uploads/')) {
      const filename = originalUrl.split('/').pop() || '';
      
      // If filename has UUID, try without UUID
      if (filename.includes('-') && filename.split('-').length > 2) {
        const parts = filename.split('-');
        const simpleFilename = `${parts[0]}-${parts[1]}.jpg`;
        fallbacks.push(`${API_URL}/uploads/${simpleFilename}`);
      }
      
      // Try with different extensions
      const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
      fallbacks.push(`${API_URL}/uploads/${nameWithoutExt}.png`);
      fallbacks.push(`${API_URL}/uploads/${nameWithoutExt}.jpeg`);
    }
    
    return fallbacks;
  };

  // Initialize current source
  useEffect(() => {
    setCurrentSrc(getImageUrl(src));
    setHasError(false);
    setAttemptCount(0);
  }, [src]);

  const handleImageError = () => {
    console.error('❌ [ImageWithFallback] Failed to load image:', currentSrc);
    
    // Try fallback URLs
    const fallbackUrls = getFallbackUrls(currentSrc);
    
    if (attemptCount < fallbackUrls.length) {
      const fallbackUrl = fallbackUrls[attemptCount];
      console.log(`🔄 [ImageWithFallback] Trying fallback ${attemptCount + 1}:`, fallbackUrl);
      setAttemptCount(prev => prev + 1);
      setCurrentSrc(fallbackUrl);
      return;
    }

    // All fallbacks failed
    console.error('❌ [ImageWithFallback] All fallback attempts failed for:', src);
    setHasError(true);
    onError?.();
  };

  const handleImageLoad = () => {
    console.log('✅ [ImageWithFallback] Image loaded successfully:', currentSrc);
    setHasError(false);
    onLoad?.();
  };

  if (hasError) {
    return (
      <div className={`bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`}>
        {fallbackText}
      </div>
    );
  }

  return (
    <img
      src={currentSrc}
      alt={alt}
      className={className}
      onError={handleImageError}
      onLoad={handleImageLoad}
    />
  );
};

export default ImageWithFallback;
