import { Request, Response } from 'express';
import { Campaign, ICampaign } from '../models/Campaign';
import { Donation } from '../models/Donation';
import { User } from '../models/user.model';
import { notifyNewCampaign, notifyCampaignDeleted } from '../services/notification.service';
import { Document, Types } from 'mongoose'; // Import Document type and Types
import fs from 'fs';
import path from 'path';
import { uploadToCloudinary } from '../services/cloudinary';
import mongoose from 'mongoose';

enum CampaignStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

interface CreateCampaignParams {
  title: string;
  description: string;
  targetAmount: number;
  startDate: Date;
  endDate: Date;
  category: string;
  images?: string[];
}

interface UpdateCampaignParams {
  title?: string;
  description?: string;
  targetAmount?: number;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  status?: CampaignStatus;
  images?: string[];
}

// Giả định đã có model Campaign và middleware upload

// @desc    Create new campaign
// @route   POST /api/admin/campaigns
// @access  Private (admin)
export const createCampaign = async (req: Request, res: Response) => {
  try {
    console.log('🆕 [Campaign] Create campaign called');
    console.log('📝 [Campaign] Request body:', JSON.stringify(req.body, null, 2));
    console.log('📁 [Campaign] Files:', req.files ? (req.files as Express.Multer.File[]).length : 0);
    console.log('👤 [Campaign] User:', req.user ? req.user.name : 'No user');

    if (!req.user) {
      console.log('❌ [Campaign] Unauthorized - no user');
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const {
      title,
      description,
      targetAmount,
      startDate,
      endDate,
      category
    } = req.body as CreateCampaignParams;

    // Validate required fields
    console.log('🔍 [Campaign] Validating fields:', { title, description, targetAmount, startDate, endDate, category });
    if (!title || !description || !targetAmount || !startDate || !endDate) {
      console.log('❌ [Campaign] Missing required fields');
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    if (start >= end) {
      return res.status(400).json({ message: 'End date must be after start date' });
    }

    // Upload images
    let imageUrls: string[] = [];
    if (req.files && (req.files as Express.Multer.File[]).length > 0) {
      console.log('📁 [Campaign] Processing files:', (req.files as Express.Multer.File[]).length);

      // Check if Cloudinary is configured
      const hasCloudinaryConfig = process.env.CLOUDINARY_CLOUD_NAME &&
                                  process.env.CLOUDINARY_API_KEY &&
                                  process.env.CLOUDINARY_API_SECRET;

      if (!hasCloudinaryConfig) {
        console.log('⚠️ [Campaign] Cloudinary not configured, using local file paths');
        // Use local file paths as fallback
        imageUrls = (req.files as Express.Multer.File[]).map(file => {
          const filename = file.filename;
          const relativePath = `/uploads/${filename}`;
          console.log('📁 [Campaign] Using local file path:', relativePath);
          return relativePath;
        });
        console.log('✅ [Campaign] Using local images:', imageUrls.length);
      } else {
        try {
          imageUrls = await Promise.all(
            (req.files as Express.Multer.File[]).map(file => uploadToCloudinary(file.path))
          );
          console.log('✅ [Campaign] Files uploaded to Cloudinary successfully:', imageUrls.length);
        } catch (uploadError) {
          console.error('❌ [Campaign] Error uploading to Cloudinary:', uploadError);
          // Fallback to local paths
          console.log('⚠️ [Campaign] Falling back to local file paths');
          imageUrls = (req.files as Express.Multer.File[]).map(file => {
            const filename = file.filename;
            const relativePath = `/uploads/${filename}`;
            console.log('📁 [Campaign] Fallback to local path:', relativePath);
            return relativePath;
          });
          console.log('✅ [Campaign] Using local images as fallback:', imageUrls.length);
        }
      }
    } else {
      console.log('📁 [Campaign] No files to upload');
    }

    console.log('💾 [Campaign] Creating campaign with data:', {
      title,
      description,
      targetAmount,
      startDate: start,
      endDate: end,
      category: category || 'Khác',
      imagesCount: imageUrls.length,
      createdBy: req.user._id
    });

    const campaign = new Campaign({
      title,
      description,
      targetAmount,
      startDate: start,
      endDate: end,
      category: category || 'Khác', // Default category
      images: imageUrls,
      image: imageUrls.length > 0 ? imageUrls[0] : '', // Set first image as main image
      createdBy: req.user._id,
      status: CampaignStatus.ACTIVE
    });

    console.log('💾 [Campaign] Saving campaign to database...');
    await campaign.save();
    console.log('✅ [Campaign] Campaign saved successfully with ID:', campaign._id);

    // Send notification to all users about new campaign
    try {
      const allUsers = await User.find({ role: 'user' }).select('_id');
      const userIds = allUsers.map(user => user._id);

      if (userIds.length > 0) {
        await notifyNewCampaign(userIds, campaign.title, campaign._id);
        console.log('📧 [Campaign] Notification sent to', userIds.length, 'users');
      }
    } catch (notificationError) {
      console.error('❌ [Campaign] Error sending notification:', notificationError);
      // Don't fail the campaign creation if notification fails
    }

    res.status(201).json(campaign);
  } catch (error) {
    console.error('❌ [Campaign] Error creating campaign:', error);
    console.error('❌ [Campaign] Error stack:', error instanceof Error ? error.stack : 'No stack');
    res.status(500).json({
      message: 'Lỗi khi tạo chiến dịch',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Helper function để cập nhật trạng thái chiến dịch
const updateCampaignStatuses = async () => {
  try {
    const now = new Date();

    // Tạo endOfDay để so sánh - chiến dịch kết thúc vào cuối ngày endDate (UTC)
    const getEndOfDay = (date: Date) => {
      const endOfDay = new Date(date);
      endOfDay.setUTCHours(23, 59, 59, 999);
      return endOfDay;
    };

    // Tìm các chiến dịch cần cập nhật trạng thái
    const campaignsToUpdate = await Campaign.find({
      $or: [
        // Chiến dịch đã hết hạn nhưng chưa completed (so sánh với cuối ngày)
        {
          status: { $in: ['active', 'draft'] }
        },
        // Chiến dịch đã đạt mục tiêu nhưng chưa completed
        {
          $expr: { $gte: ['$currentAmount', '$targetAmount'] },
          status: { $in: ['active', 'draft'] }
        },
        // Chiến dịch đã bắt đầu nhưng vẫn là draft
        {
          startDate: { $lte: now },
          status: 'draft'
        }
      ]
    });

    for (const campaign of campaignsToUpdate) {
      const hasReachedTarget = campaign.currentAmount >= campaign.targetAmount;
      const endOfEndDate = getEndOfDay(campaign.endDate);
      const isExpired = now > endOfEndDate; // So sánh với cuối ngày endDate
      const shouldBeActive = now >= campaign.startDate && now <= endOfEndDate && !hasReachedTarget;

      let newStatus = campaign.status;

      if (hasReachedTarget || isExpired) {
        newStatus = 'completed';
      } else if (shouldBeActive && campaign.status === 'draft') {
        newStatus = 'active';
      }

      if (newStatus !== campaign.status) {
        const newProgress = Math.min(100, (campaign.currentAmount / campaign.targetAmount) * 100);

        await Campaign.updateOne(
          { _id: campaign._id },
          {
            $set: {
              status: newStatus,
              progress: newProgress,
              updatedAt: new Date()
            }
          }
        );

        console.log(`✅ Updated campaign "${campaign.title}": ${campaign.status} → ${newStatus} ${hasReachedTarget ? '(reached target)' : isExpired ? '(expired)' : '(started)'}`);
      }
    }
  } catch (error) {
    console.error('Error updating campaign statuses:', error);
  }
};

// @desc    Get all campaigns
// @route   GET /api/admin/campaigns
// @access  Private (admin)
export const getCampaigns = async (req: Request, res: Response) => {
  try {
    // Cập nhật trạng thái chiến dịch trước khi fetch
    await updateCampaignStatuses();

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const [campaigns, total] = await Promise.all([
      Campaign.find()
        .populate('createdBy', 'name email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      Campaign.countDocuments()
    ]);

    res.json({
      success: true,
      campaigns,
      data: campaigns, // Add data field for consistency
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting campaigns:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi lấy danh sách chiến dịch',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// @desc    Get public campaigns
// @route   GET /api/campaigns
// @access  Public
export const getPublicCampaigns = async (req: Request, res: Response) => {
  try {
    // Cập nhật trạng thái chiến dịch trước khi fetch
    await updateCampaignStatuses();

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Trả về cả chiến dịch active và completed để frontend có thể hiển thị trong 2 tab
    const [campaigns, total] = await Promise.all([
      Campaign.find({
        status: { $in: ['active', 'completed'] }
      })
        .populate('createdBy', 'name email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      Campaign.countDocuments({
        status: { $in: ['active', 'completed'] }
      })
    ]);

    res.json({
      success: true,
      data: campaigns,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting public campaigns:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi lấy danh sách chiến dịch',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// @desc    Get campaign by ID
// @route   GET /api/campaigns/:id
// @access  Public
export const getCampaignById = async (req: Request, res: Response) => {
  try {
    // Cập nhật trạng thái chiến dịch trước khi fetch
    await updateCampaignStatuses();

    const campaign = await Campaign.findById(req.params.id)
      .populate('createdBy', 'name email');

    if (!campaign) {
      return res.status(404).json({ message: 'Không tìm thấy chiến dịch' });
    }

    res.json(campaign);
  } catch (error) {
    console.error('Error getting campaign by ID:', error);
    res.status(500).json({
      message: 'Lỗi khi lấy thông tin chiến dịch',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// @desc    Update campaign
// @route   PUT /api/admin/campaigns/:id
// @access  Private (admin)
export const updateCampaign = async (req: Request, res: Response) => {
  console.log('🔄 [Campaign] ===== UPDATE CAMPAIGN START =====');
  console.log('🔄 [Campaign] Update campaign called for ID:', req.params.id);
  console.log('📝 [Campaign] Request body:', JSON.stringify(req.body, null, 2));
  console.log('📁 [Campaign] Files:', req.files ? (req.files as Express.Multer.File[]).length : 0);
  console.log('👤 [Campaign] User:', req.user ? req.user.name : 'No user');

  try {

    const {
      title,
      description,
      targetAmount,
      startDate,
      endDate,
      category,
      status,
      existingImages
    } = req.body as UpdateCampaignParams & { existingImages?: string | string[] };

    // Validate campaign ID
    if (!mongoose.Types.ObjectId.isValid(req.params.id)) {
      console.log('❌ [Campaign] Invalid campaign ID:', req.params.id);
      return res.status(400).json({ message: 'ID chiến dịch không hợp lệ' });
    }

    const campaign = await Campaign.findById(req.params.id);
    if (!campaign) {
      console.log('❌ [Campaign] Campaign not found:', req.params.id);
      return res.status(404).json({ message: 'Không tìm thấy chiến dịch' });
    }

    console.log('✅ [Campaign] Found campaign:', campaign.title);

    // Xử lý hình ảnh
    let finalImages: string[] = [];

    // Thêm các hình ảnh hiện có được giữ lại
    console.log('📁 [Campaign] Raw existingImages:', existingImages);
    console.log('📁 [Campaign] existingImages type:', typeof existingImages);
    console.log('📁 [Campaign] existingImages isArray:', Array.isArray(existingImages));

    if (existingImages) {
      if (Array.isArray(existingImages)) {
        finalImages = [...existingImages.filter(img => img && img.trim())]; // Filter out empty strings
      } else if (typeof existingImages === 'string' && existingImages.trim()) {
        finalImages = [existingImages];
      }
      console.log('📁 [Campaign] Keeping existing images:', finalImages.length, finalImages);
    } else {
      console.log('📁 [Campaign] No existing images to keep');
    }

    // Upload và thêm hình ảnh mới nếu có
    if (req.files && (req.files as Express.Multer.File[]).length > 0) {
      console.log('📁 [Campaign] Uploading new images:', (req.files as Express.Multer.File[]).length);

      // Check if Cloudinary is configured
      const hasCloudinaryConfig = process.env.CLOUDINARY_CLOUD_NAME &&
                                  process.env.CLOUDINARY_API_KEY &&
                                  process.env.CLOUDINARY_API_SECRET;

      if (!hasCloudinaryConfig) {
        console.log('⚠️ [Campaign] Cloudinary not configured, using local file paths');
        // Use local file paths as fallback - Store simple relative paths
        const newImageUrls = (req.files as Express.Multer.File[]).map(file => {
          // Use the filename directly from multer
          const filename = file.filename;
          const relativePath = `/uploads/${filename}`;
          console.log('📁 [Campaign] Using local file path:', relativePath);
          console.log('📁 [Campaign] File details:', {
            filename: file.filename,
            originalname: file.originalname,
            path: file.path,
            destination: file.destination
          });
          return relativePath; // Store relative path, not full URL
        });
        finalImages = [...finalImages, ...newImageUrls];
        console.log('✅ [Campaign] Using local images:', newImageUrls.length);
      } else {
        try {
          const newImageUrls = await Promise.all(
            (req.files as Express.Multer.File[]).map(async (file) => {
              console.log('📁 [Campaign] Uploading file to Cloudinary:', file.path);
              const url = await uploadToCloudinary(file.path);
              console.log('✅ [Campaign] File uploaded to Cloudinary:', url);
              return url;
            })
          );
          finalImages = [...finalImages, ...newImageUrls];
          console.log('✅ [Campaign] New images uploaded to Cloudinary successfully:', newImageUrls.length);
        } catch (uploadError) {
          console.error('❌ [Campaign] Error uploading new images to Cloudinary:', uploadError);
          console.error('❌ [Campaign] Upload error stack:', uploadError instanceof Error ? uploadError.stack : 'No stack');
          // Fallback to local paths
          console.log('⚠️ [Campaign] Falling back to local file paths');
          const newImageUrls = (req.files as Express.Multer.File[]).map(file => {
            // Use the filename directly from multer
            const filename = file.filename;
            const relativePath = `/uploads/${filename}`;
            console.log('📁 [Campaign] Fallback to local path:', relativePath);
            return relativePath; // Store relative path, not full URL
          });
          finalImages = [...finalImages, ...newImageUrls];
          console.log('✅ [Campaign] Using local images as fallback:', newImageUrls.length);
        }
      }
    }



    // Prepare update object
    const updateData: any = {};

    if (title) {
      updateData.title = title;
      console.log('📝 [Campaign] Updated title:', title);
    }
    if (description) {
      updateData.description = description;
      console.log('📝 [Campaign] Updated description length:', description.length);
    }
    if (targetAmount) {
      updateData.targetAmount = Number(targetAmount);
      console.log('📝 [Campaign] Updated targetAmount:', targetAmount);
    }
    if (startDate) {
      updateData.startDate = new Date(startDate);
      console.log('📝 [Campaign] Updated startDate:', startDate);
    }
    if (endDate) {
      updateData.endDate = new Date(endDate);
      console.log('📝 [Campaign] Updated endDate:', endDate);
    }
    if (category) {
      updateData.category = category;
      console.log('📝 [Campaign] Updated category:', category);
    }
    if (status) {
      updateData.status = status;
      console.log('📝 [Campaign] Updated status:', status);
    }

    // Update images - always update to reflect current state
    updateData.images = finalImages;
    updateData.image = finalImages.length > 0 ? finalImages[0] : '';
    console.log('📁 [Campaign] Updated images count:', finalImages.length);
    console.log('📁 [Campaign] Final images array:', finalImages);

    console.log('💾 [Campaign] Updating campaign with data:', Object.keys(updateData));
    const updatedCampaign = await Campaign.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: false } // Skip validation to avoid createdBy issue
    );

    if (!updatedCampaign) {
      return res.status(404).json({ message: 'Không tìm thấy chiến dịch sau khi cập nhật' });
    }

    console.log('✅ [Campaign] Campaign updated successfully');
    res.json(updatedCampaign);
  } catch (error) {
    console.error('❌ [Campaign] ===== UPDATE CAMPAIGN ERROR =====');
    console.error('❌ [Campaign] Error updating campaign:', error);
    console.error('❌ [Campaign] Error stack:', error instanceof Error ? error.stack : 'No stack');
    console.error('❌ [Campaign] ===== END ERROR =====');
    res.status(500).json({
      message: 'Lỗi khi cập nhật chiến dịch',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// @desc    Delete campaign
// @route   DELETE /api/admin/campaigns/:id
// @access  Private (admin)
export const deleteCampaign = async (req: Request, res: Response) => {
  try {
    const { reason } = req.body; // Get deletion reason from request body

    const campaign = await Campaign.findById(req.params.id);
    if (!campaign) {
      return res.status(404).json({ message: 'Không tìm thấy chiến dịch' });
    }

    // Find all donors who donated to this campaign
    try {
      const donations = await Donation.find({
        campaignId: campaign._id,
        status: 'completed'
      }).distinct('userId');

      if (donations.length > 0) {
        await notifyCampaignDeleted(
          donations,
          campaign.title,
          campaign._id,
          reason || 'Chiến dịch đã bị hủy bởi quản trị viên'
        );
        console.log('📧 [Campaign] Deletion notification sent to', donations.length, 'donors');
      }
    } catch (notificationError) {
      console.error('❌ [Campaign] Error sending deletion notification:', notificationError);
      // Don't fail the deletion if notification fails
    }

    await campaign.deleteOne();
    res.json({ message: 'Chiến dịch đã được xóa' });
  } catch (error) {
    console.error('Error deleting campaign:', error);
    res.status(500).json({
      message: 'Lỗi khi xóa chiến dịch',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// @desc    Update campaign status
// @route   PATCH /api/admin/campaigns/:id/status
// @access  Private (admin)
export const updateCampaignStatus = async (req: Request, res: Response) => {
  try {
    const { status } = req.body;
    if (!status || !Object.values(CampaignStatus).includes(status as CampaignStatus)) {
      return res.status(400).json({ message: 'Invalid status' });
    }

    const campaign = await Campaign.findById(req.params.id);
    if (!campaign) {
      return res.status(404).json({ message: 'Không tìm thấy chiến dịch' });
    }

    campaign.status = status as CampaignStatus;
    await campaign.save();
    
    res.json(campaign);
  } catch (error) {
    console.error('Error updating campaign status:', error);
    res.status(500).json({ 
      message: 'Lỗi khi cập nhật trạng thái chiến dịch',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// @desc    Manual update campaign statuses
// @route   POST /api/admin/campaigns/update-statuses
// @access  Private (admin)
export const manualUpdateCampaignStatuses = async (req: Request, res: Response) => {
  try {
    console.log('🔄 Manual campaign status update triggered by admin');
    await updateCampaignStatuses();

    // Get updated counts
    const [activeCount, completedCount, draftCount] = await Promise.all([
      Campaign.countDocuments({ status: 'active' }),
      Campaign.countDocuments({ status: 'completed' }),
      Campaign.countDocuments({ status: 'draft' })
    ]);

    res.json({
      success: true,
      message: 'Trạng thái chiến dịch đã được cập nhật',
      counts: {
        active: activeCount,
        completed: completedCount,
        draft: draftCount
      }
    });
  } catch (error) {
    console.error('Error in manual campaign status update:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi cập nhật trạng thái chiến dịch',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// @desc    Fix campaign images paths
// @route   POST /api/campaigns/admin/fix-images
// @access  Private (admin)
export const fixCampaignImages = async (req: Request, res: Response) => {
  try {
    console.log('🔧 [Campaign] Starting image path fix...');

    const fs = await import('fs');
    const path = await import('path');

    // Get all campaigns
    const campaigns = await Campaign.find({});
    console.log(`📊 [Campaign] Found ${campaigns.length} campaigns to check`);

    // Get all files in uploads directory
    const uploadsDir = path.join(__dirname, '../uploads');
    const files = fs.readdirSync(uploadsDir).filter(file =>
      file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.gif')
    );
    console.log(`📁 [Campaign] Found ${files.length} image files in uploads`);

    let fixedCount = 0;

    for (const campaign of campaigns) {
      let hasChanges = false;
      const newImages: string[] = [];

      for (const imagePath of campaign.images) {
        // Extract filename from path
        const filename = imagePath.split('/').pop() || imagePath;

        // Check if file exists in uploads
        if (files.includes(filename)) {
          newImages.push(`/uploads/${filename}`);
          console.log(`✅ [Campaign] Fixed image path: ${imagePath} -> /uploads/${filename}`);
          hasChanges = true;
        } else {
          // Try to find similar filename
          const similarFile = files.find(file =>
            file.includes(filename.split('-')[0]) ||
            filename.includes(file.split('-')[0])
          );

          if (similarFile) {
            newImages.push(`/uploads/${similarFile}`);
            console.log(`🔄 [Campaign] Mapped image: ${imagePath} -> /uploads/${similarFile}`);
            hasChanges = true;
          } else {
            console.log(`❌ [Campaign] No matching file found for: ${imagePath}`);
            // Don't include this image
          }
        }
      }

      if (hasChanges) {
        campaign.images = newImages;
        await campaign.save();
        fixedCount++;
        console.log(`✅ [Campaign] Fixed campaign: ${campaign.title}`);
      }
    }

    console.log(`🎉 [Campaign] Fixed ${fixedCount} campaigns`);

    res.json({
      success: true,
      message: `Fixed image paths for ${fixedCount} campaigns`,
      totalCampaigns: campaigns.length,
      fixedCampaigns: fixedCount,
      availableFiles: files
    });
  } catch (error) {
    console.error('❌ [Campaign] Error fixing images:', error);
    res.status(500).json({
      success: false,
      message: 'Error fixing campaign images',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export const createCampaignExport = createCampaign;
export const getCampaignsExport = getCampaigns;
export const updateCampaignExport = updateCampaign;
export const deleteCampaignExport = deleteCampaign;
export const updateCampaignStatusExport = updateCampaignStatus;
export const getPublicCampaignsExport = getPublicCampaigns;
export const getCampaignByIdExport = getCampaignById;