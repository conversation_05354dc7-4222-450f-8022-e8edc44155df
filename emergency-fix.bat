@echo off
echo ========================================
echo    EMERGENCY DATABASE FIX
echo ========================================
echo.
echo This will fix the image path issues immediately
echo.
echo Step 1: Installing MongoDB driver...
npm install mongodb
echo.
echo Step 2: Running database fix...
node quick-fix-database.js
echo.
echo Step 3: Restarting frontend (if running)...
echo Please restart your frontend manually:
echo cd frontend
echo npm run dev
echo.
echo ========================================
echo    FIX COMPLETED
echo ========================================
echo.
echo The console errors should now be reduced significantly.
echo If you still see some errors, they will stop after the
echo database fix takes effect.
echo.
pause
