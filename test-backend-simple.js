const http = require('http');

// Test if backend is running
function testBackend() {
  console.log('🔍 Testing backend connection...');
  
  const options = {
    hostname: 'localhost',
    port: 5001,
    path: '/api/campaigns/fix-images',
    method: 'GET',
    timeout: 5000
  };

  const req = http.request(options, (res) => {
    console.log(`✅ Backend is running! Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        console.log('📊 Response:', result);
      } catch (e) {
        console.log('📄 Response (text):', data);
      }
    });
  });

  req.on('error', (err) => {
    console.error('❌ Backend connection failed:', err.message);
    console.log('💡 Try starting backend with: cd backend && npm start');
  });

  req.on('timeout', () => {
    console.error('⏰ Backend connection timeout');
    req.destroy();
  });

  req.end();
}

// Test static file serving
function testStaticFiles() {
  console.log('\n🔍 Testing static file serving...');
  
  const testFiles = [
    'images-1747917385255.jpg',
    'images-1747917385280.jpg',
    'images-1748012855368.jpg'
  ];
  
  testFiles.forEach(filename => {
    const options = {
      hostname: 'localhost',
      port: 5001,
      path: `/uploads/${filename}`,
      method: 'HEAD',
      timeout: 3000
    };

    const req = http.request(options, (res) => {
      if (res.statusCode === 200) {
        console.log(`✅ File accessible: ${filename}`);
      } else {
        console.log(`❌ File not found: ${filename} (Status: ${res.statusCode})`);
      }
    });

    req.on('error', (err) => {
      console.error(`❌ Error accessing ${filename}:`, err.message);
    });

    req.on('timeout', () => {
      console.error(`⏰ Timeout accessing ${filename}`);
      req.destroy();
    });

    req.end();
  });
}

// Run tests
testBackend();
setTimeout(testStaticFiles, 2000);
