import multer from 'multer';
import path from 'path';
import fs from 'fs';

// <PERSON><PERSON><PERSON> bảo thư mục uploads tồn tại
const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Cấu hình storage cho multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    try {
      console.log('📁 [Upload] Processing file upload for path:', req.path, 'method:', req.method);

      // Luôn sử dụng thư mục uploads ch<PERSON>h để tránh phức tạp
      console.log('📁 [Upload] Using main upload directory:', uploadDir);
      cb(null, uploadDir);
    } catch (error) {
      console.error('❌ [Upload] Error in destination function:', error);
      cb(error as Error, uploadDir); // Fallback to default directory
    }
  },
  filename: function (req, file, cb) {
    // Tạo tên file duy nhất với timestamp (không dùng uuid để tránh tên file quá dài)
    const uniqueSuffix = Date.now();
    const filename = file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname);
    console.log('📁 [Upload] Generated filename:', filename);
    cb(null, filename);
  }
});

// Kiểm tra file type
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  console.log('🔍 [Upload] Checking file type:', file.mimetype, 'for file:', file.originalname);

  // Chỉ chấp nhận các file ảnh
  if (file.mimetype.startsWith('image/')) {
    console.log('✅ [Upload] File type accepted:', file.mimetype);
    cb(null, true);
  } else {
    console.log('❌ [Upload] File type rejected:', file.mimetype);
    cb(new Error('Chỉ chấp nhận file ảnh!'));
  }
};

// Cấu hình multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // Giới hạn 5MB
    files: 5 // Tối đa 5 file
  }
});

export default upload; 