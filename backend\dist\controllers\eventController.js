"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateShareQRCode = exports.checkInWithQRCode = exports.generateUserQRCode = exports.createEventAdmin = exports.getAllEventsAdmin = exports.getEventRegistrations = exports.getUserRegistrations = exports.cancelEventRegistration = exports.registerForEvent = exports.getEventById = exports.getEvents = void 0;
const Event_1 = __importDefault(require("../models/Event"));
const EventRegistration_1 = __importDefault(require("../models/EventRegistration"));
const user_model_1 = require("../models/user.model");
const notification_service_1 = require("../services/notification.service");
const eventEmailService_1 = require("../services/eventEmailService");
const qrCodeService_1 = require("../services/qrCodeService");
const mongoose_1 = __importDefault(require("mongoose"));
// Lấy danh sách sự kiện (public)
const getEvents = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 12, status, search, sortBy = 'eventDate', sortOrder = 'asc' } = req.query;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;
        // Build filter
        const filter = {};
        if (status && status !== 'all') {
            filter.status = status;
        }
        if (search) {
            filter.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { location: { $regex: search, $options: 'i' } }
            ];
        }
        // Build sort
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
        const events = yield Event_1.default.find(filter)
            .populate('createdBy', 'name email')
            .sort(sortOptions)
            .skip(skip)
            .limit(limitNum)
            .lean();
        const total = yield Event_1.default.countDocuments(filter);
        res.json({
            success: true,
            data: events,
            pagination: {
                currentPage: pageNum,
                totalPages: Math.ceil(total / limitNum),
                totalEvents: total,
                hasNext: pageNum < Math.ceil(total / limitNum),
                hasPrev: pageNum > 1
            }
        });
    }
    catch (error) {
        console.error('Error fetching events:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải danh sách sự kiện',
            error: error.message
        });
    }
});
exports.getEvents = getEvents;
// Lấy chi tiết sự kiện
const getEventById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const event = yield Event_1.default.findById(id)
            .populate('createdBy', 'name email')
            .lean();
        if (!event) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy sự kiện'
            });
        }
        // Lấy danh sách người đăng ký (chỉ số lượng cho public)
        const registrationCount = yield EventRegistration_1.default.countDocuments({
            eventId: event._id,
            status: 'registered'
        });
        res.json({
            success: true,
            event: Object.assign(Object.assign({}, event), { currentParticipants: registrationCount })
        });
    }
    catch (error) {
        console.error('Error fetching event:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải thông tin sự kiện',
            error: error.message
        });
    }
});
exports.getEventById = getEventById;
// Đăng ký tham gia sự kiện
const registerForEvent = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        console.log('🎯 [REGISTER EVENT] Starting registration process');
        console.log('🎯 [REGISTER EVENT] Event ID:', req.params.id);
        console.log('🎯 [REGISTER EVENT] User from req:', req.user);
        console.log('🎯 [REGISTER EVENT] Request body:', req.body);
        const { id } = req.params;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        console.log('🎯 [REGISTER EVENT] Extracted user ID:', userId);
        if (!userId) {
            console.log('❌ [REGISTER EVENT] No user ID found');
            return res.status(401).json({
                success: false,
                message: 'Vui lòng đăng nhập để đăng ký sự kiện'
            });
        }
        // Kiểm tra sự kiện tồn tại
        console.log('🔍 [REGISTER EVENT] Looking for event with ID:', id);
        const event = yield Event_1.default.findById(id);
        if (!event) {
            console.log('❌ [REGISTER EVENT] Event not found');
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy sự kiện'
            });
        }
        console.log('✅ [REGISTER EVENT] Event found:', event.title);
        // Kiểm tra điều kiện đăng ký
        const now = new Date();
        console.log('⏰ [REGISTER EVENT] Current time:', now);
        console.log('⏰ [REGISTER EVENT] Registration deadline:', event.registrationDeadline);
        console.log('⏰ [REGISTER EVENT] Event status:', event.status);
        if (now >= event.registrationDeadline) {
            console.log('❌ [REGISTER EVENT] Registration deadline passed');
            return res.status(400).json({
                success: false,
                message: 'Đã hết hạn đăng ký cho sự kiện này'
            });
        }
        if (event.status !== 'upcoming') {
            console.log('❌ [REGISTER EVENT] Event status not upcoming:', event.status);
            return res.status(400).json({
                success: false,
                message: 'Sự kiện này không còn nhận đăng ký'
            });
        }
        // Kiểm tra số lượng
        const currentRegistrations = yield EventRegistration_1.default.countDocuments({
            eventId: event._id,
            status: 'registered'
        });
        if (currentRegistrations >= event.maxParticipants) {
            return res.status(400).json({
                success: false,
                message: 'Sự kiện đã đủ số lượng người tham gia'
            });
        }
        // Kiểm tra đã đăng ký chưa (chỉ kiểm tra status 'registered')
        const existingRegistration = yield EventRegistration_1.default.findOne({
            eventId: event._id,
            userId: userId,
            status: 'registered'
        });
        if (existingRegistration) {
            console.log('❌ [REGISTER EVENT] User already registered with status:', existingRegistration.status);
            return res.status(400).json({
                success: false,
                message: 'Bạn đã đăng ký sự kiện này rồi'
            });
        }
        // Kiểm tra xem có registration cũ bị hủy không
        const cancelledRegistration = yield EventRegistration_1.default.findOne({
            eventId: event._id,
            userId: userId,
            status: 'cancelled'
        });
        if (cancelledRegistration) {
            console.log('🔄 [REGISTER EVENT] Found cancelled registration, will reactivate it');
            // Reactivate the cancelled registration instead of creating new one
            cancelledRegistration.status = 'registered';
            cancelledRegistration.registrationDate = new Date();
            yield cancelledRegistration.save();
            // Cập nhật số lượng người tham gia
            yield Event_1.default.findByIdAndUpdate(event._id, {
                $inc: { currentParticipants: 1 }
            });
            // Send notification for reactivated registration
            try {
                const { createNotification } = yield Promise.resolve().then(() => __importStar(require('../services/notification.service')));
                yield createNotification({
                    userId: userId,
                    type: 'event',
                    title: 'Đăng ký lại sự kiện thành công!',
                    message: `Bạn đã đăng ký lại thành công sự kiện "${event.title}". Chúng tôi sẽ gửi thông báo nhắc nhở trước khi sự kiện diễn ra.`,
                    data: {
                        eventId: new mongoose_1.default.Types.ObjectId(event._id),
                        actionType: 'updated'
                    },
                    priority: 'medium',
                    sendEmail: false
                });
                console.log('✅ Re-registration notification created for user');
            }
            catch (error) {
                console.error('❌ Failed to send re-registration notification:', error);
            }
            // Check and award volunteer badge for reactivated registration
            try {
                const { checkVolunteerBadge } = yield Promise.resolve().then(() => __importStar(require('../services/badge.service')));
                const badgeResult = yield checkVolunteerBadge(userId);
                if (badgeResult.earned && badgeResult.levelUp) {
                    console.log('🏆 [Event] Badge awarded/upgraded for reactivated user:', userId);
                }
            }
            catch (badgeError) {
                console.error('❌ [Event] Error checking badges for reactivated registration:', badgeError);
            }
            return res.status(201).json({
                success: true,
                message: 'Đăng ký sự kiện thành công',
                registration: {
                    _id: cancelledRegistration._id,
                    eventId: cancelledRegistration.eventId,
                    userId: cancelledRegistration.userId,
                    status: cancelledRegistration.status,
                    registrationDate: cancelledRegistration.registrationDate,
                    participantInfo: cancelledRegistration.participantInfo
                }
            });
        }
        // Get user info
        const userDoc = yield user_model_1.User.findById(userId);
        if (!userDoc) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy thông tin người dùng'
            });
        }
        // Tạo đăng ký mới với thông tin từ user
        const registration = new EventRegistration_1.default({
            eventId: event._id,
            userId: userId,
            notes: '',
            participantInfo: {
                fullName: userDoc.name || userDoc.email || 'Người dùng',
                phone: userDoc.phone || '',
                email: userDoc.email || ''
            }
        });
        yield registration.save();
        // Cập nhật số lượng người tham gia
        yield Event_1.default.findByIdAndUpdate(event._id, {
            $inc: { currentParticipants: 1 }
        });
        // Gửi email xác nhận đăng ký và tạo in-app notification
        try {
            // Send email confirmation
            if (userDoc && process.env.SMTP_USER && process.env.SMTP_PASS) {
                yield (0, eventEmailService_1.sendEventRegistrationEmail)(userDoc, event);
                console.log('✅ Registration email sent successfully');
            }
            else {
                console.log('⚠️ Email service not configured, skipping email');
            }
            // Create in-app notification
            const { createNotification } = yield Promise.resolve().then(() => __importStar(require('../services/notification.service')));
            yield createNotification({
                userId: userId,
                type: 'event',
                title: 'Đăng ký sự kiện thành công!',
                message: `Bạn đã đăng ký thành công sự kiện "${event.title}". Chúng tôi sẽ gửi thông báo nhắc nhở trước khi sự kiện diễn ra.`,
                data: {
                    eventId: new mongoose_1.default.Types.ObjectId(event._id),
                    actionType: 'created'
                },
                priority: 'medium',
                sendEmail: false // Already sent email above
            });
            console.log('✅ Registration notification created for user');
        }
        catch (error) {
            console.error('❌ Failed to send registration notifications:', error);
            // Không throw error để không ảnh hưởng đến quá trình đăng ký
        }
        // Check and award volunteer badge
        try {
            const { checkVolunteerBadge } = yield Promise.resolve().then(() => __importStar(require('../services/badge.service')));
            const badgeResult = yield checkVolunteerBadge(userId);
            if (badgeResult.earned && badgeResult.levelUp) {
                console.log('🏆 [Event] Badge awarded/upgraded for user:', userId);
            }
        }
        catch (badgeError) {
            console.error('❌ [Event] Error checking badges:', badgeError);
            // Don't fail the registration if badge check fails
        }
        res.status(201).json({
            success: true,
            message: 'Đăng ký sự kiện thành công',
            registration: {
                _id: registration._id,
                eventId: registration.eventId,
                userId: registration.userId,
                status: registration.status,
                registrationDate: registration.registrationDate,
                participantInfo: registration.participantInfo
            }
        });
    }
    catch (error) {
        console.error('Error registering for event:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi đăng ký sự kiện',
            error: error.message
        });
    }
});
exports.registerForEvent = registerForEvent;
// Hủy đăng ký sự kiện
const cancelEventRegistration = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { id } = req.params;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'Vui lòng đăng nhập'
            });
        }
        const registration = yield EventRegistration_1.default.findOne({
            eventId: id,
            userId: userId,
            status: 'registered'
        });
        if (!registration) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy đăng ký của bạn cho sự kiện này'
            });
        }
        // Kiểm tra thời gian hủy (có thể hủy trước 24h)
        const event = yield Event_1.default.findById(id);
        if (event) {
            const cancelDeadline = new Date(event.eventDate);
            cancelDeadline.setHours(cancelDeadline.getHours() - 24);
            if (new Date() >= cancelDeadline) {
                return res.status(400).json({
                    success: false,
                    message: 'Không thể hủy đăng ký trong vòng 24h trước sự kiện'
                });
            }
        }
        registration.status = 'cancelled';
        yield registration.save();
        // Giảm số lượng người tham gia
        yield Event_1.default.findByIdAndUpdate(id, {
            $inc: { currentParticipants: -1 }
        });
        res.json({
            success: true,
            message: 'Hủy đăng ký thành công'
        });
    }
    catch (error) {
        console.error('Error cancelling registration:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi hủy đăng ký',
            error: error.message
        });
    }
});
exports.cancelEventRegistration = cancelEventRegistration;
// Lấy danh sách sự kiện đã đăng ký của user
const getUserRegistrations = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'Vui lòng đăng nhập'
            });
        }
        const registrations = yield EventRegistration_1.default.find({
            userId: userId,
            status: { $in: ['registered', 'attended'] }
        })
            .populate({
            path: 'eventId',
            select: 'title description eventDate location status images'
        })
            .sort({ registrationDate: -1 });
        res.json({
            success: true,
            registrations
        });
    }
    catch (error) {
        console.error('Error fetching user registrations:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải danh sách đăng ký',
            error: error.message
        });
    }
});
exports.getUserRegistrations = getUserRegistrations;
// ==================== ADMIN FUNCTIONS ====================
// Lấy danh sách đăng ký của một sự kiện (admin)
const getEventRegistrations = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { id } = req.params;
        const { page = 1, limit = 20, status, search } = req.query;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;
        // Build filter
        const filter = { eventId: id };
        if (status && status !== 'all') {
            filter.status = status;
        }
        // Build aggregation pipeline
        const pipeline = [
            { $match: filter },
            {
                $lookup: {
                    from: 'users',
                    localField: 'userId',
                    foreignField: '_id',
                    as: 'user'
                }
            },
            { $unwind: '$user' },
            {
                $lookup: {
                    from: 'events',
                    localField: 'eventId',
                    foreignField: '_id',
                    as: 'event'
                }
            },
            { $unwind: '$event' }
        ];
        // Add search filter if provided
        if (search) {
            pipeline.push({
                $match: {
                    $or: [
                        { 'participantInfo.fullName': { $regex: search, $options: 'i' } },
                        { 'participantInfo.email': { $regex: search, $options: 'i' } },
                        { 'participantInfo.phone': { $regex: search, $options: 'i' } },
                        { 'user.name': { $regex: search, $options: 'i' } },
                        { 'user.email': { $regex: search, $options: 'i' } }
                    ]
                }
            });
        }
        // Add sorting
        pipeline.push({ $sort: { registrationDate: -1 } });
        // Get total count
        const totalPipeline = [...pipeline, { $count: 'total' }];
        const totalResult = yield EventRegistration_1.default.aggregate(totalPipeline);
        const total = ((_a = totalResult[0]) === null || _a === void 0 ? void 0 : _a.total) || 0;
        // Add pagination
        pipeline.push({ $skip: skip }, { $limit: limitNum });
        // Execute aggregation
        const registrations = yield EventRegistration_1.default.aggregate(pipeline);
        res.json({
            success: true,
            registrations,
            pagination: {
                currentPage: pageNum,
                totalPages: Math.ceil(total / limitNum),
                totalRegistrations: total,
                hasNext: pageNum < Math.ceil(total / limitNum),
                hasPrev: pageNum > 1
            }
        });
    }
    catch (error) {
        console.error('Error fetching event registrations:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải danh sách đăng ký',
            error: error.message
        });
    }
});
exports.getEventRegistrations = getEventRegistrations;
// Lấy tất cả sự kiện (admin)
const getAllEventsAdmin = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 20, status, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;
        // Build filter
        const filter = {};
        if (status && status !== 'all') {
            filter.status = status;
        }
        if (search) {
            filter.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { location: { $regex: search, $options: 'i' } }
            ];
        }
        // Build sort
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
        const events = yield Event_1.default.find(filter)
            .populate('createdBy', 'name email')
            .sort(sortOptions)
            .skip(skip)
            .limit(limitNum);
        // Lấy số lượng đăng ký cho mỗi sự kiện
        const eventsWithRegistrations = yield Promise.all(events.map((event) => __awaiter(void 0, void 0, void 0, function* () {
            const registrationCount = yield EventRegistration_1.default.countDocuments({
                eventId: event._id,
                status: 'registered'
            });
            return Object.assign(Object.assign({}, event.toObject()), { currentParticipants: registrationCount });
        })));
        const total = yield Event_1.default.countDocuments(filter);
        res.json({
            success: true,
            events: eventsWithRegistrations, // Keep for backward compatibility
            data: eventsWithRegistrations, // Add for frontend consistency
            pagination: {
                currentPage: pageNum,
                totalPages: Math.ceil(total / limitNum),
                totalEvents: total,
                hasNext: pageNum < Math.ceil(total / limitNum),
                hasPrev: pageNum > 1
            }
        });
    }
    catch (error) {
        console.error('Error fetching events (admin):', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải danh sách sự kiện',
            error: error.message
        });
    }
});
exports.getAllEventsAdmin = getAllEventsAdmin;
// Tạo sự kiện mới (admin)
const createEventAdmin = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { title, description, eventDate, registrationDeadline, location, maxParticipants, requirements, benefits } = req.body;
        const createdBy = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!createdBy) {
            return res.status(401).json({
                success: false,
                message: 'Vui lòng đăng nhập'
            });
        }
        // Use images from request body (empty array if no images)
        const imageUrls = req.body.images || [];
        console.log('🖼️ [CREATE EVENT] Images from request:', imageUrls.length);
        // Parse requirements and benefits safely
        let parsedRequirements = [];
        let parsedBenefits = [];
        try {
            parsedRequirements = requirements ? JSON.parse(requirements) : [];
        }
        catch (e) {
            console.warn('Failed to parse requirements:', e);
            parsedRequirements = [];
        }
        try {
            parsedBenefits = benefits ? JSON.parse(benefits) : [];
        }
        catch (e) {
            console.warn('Failed to parse benefits:', e);
            parsedBenefits = [];
        }
        const event = new Event_1.default({
            title,
            description,
            eventDate: new Date(eventDate),
            registrationDeadline: new Date(registrationDeadline),
            location,
            maxParticipants: parseInt(maxParticipants),
            requirements: parsedRequirements,
            benefits: parsedBenefits,
            images: imageUrls,
            createdBy
        });
        yield event.save();
        // Send notification to all users about new event
        try {
            const allUsers = yield user_model_1.User.find({ role: 'user' }).select('_id');
            const userIds = allUsers.map(user => user._id);
            if (userIds.length > 0) {
                yield (0, notification_service_1.notifyNewEvent)(userIds, event.title, new mongoose_1.default.Types.ObjectId(event._id));
                console.log('📧 [Event] Notification sent to', userIds.length, 'users');
            }
        }
        catch (notificationError) {
            console.error('❌ [Event] Error sending notification:', notificationError);
            // Don't fail the event creation if notification fails
        }
        res.status(201).json({
            success: true,
            message: 'Tạo sự kiện thành công',
            event
        });
    }
    catch (error) {
        console.error('Error creating event:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tạo sự kiện',
            error: error.message
        });
    }
});
exports.createEventAdmin = createEventAdmin;
// ==================== QR CODE FUNCTIONS ====================
// Tạo QR code cho check-in
const generateUserQRCode = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { id } = req.params; // event ID
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'Vui lòng đăng nhập'
            });
        }
        // Kiểm tra đăng ký
        const registration = yield EventRegistration_1.default.findOne({
            eventId: id,
            userId: userId,
            status: 'registered'
        });
        if (!registration) {
            return res.status(404).json({
                success: false,
                message: 'Bạn chưa đăng ký sự kiện này'
            });
        }
        // Tạo QR code
        const qrCode = yield (0, qrCodeService_1.generateCheckInQRCode)(id, userId.toString(), registration._id.toString());
        res.json({
            success: true,
            qrCode,
            registration: {
                id: registration._id,
                eventId: registration.eventId,
                status: registration.status,
                registrationDate: registration.registrationDate
            }
        });
    }
    catch (error) {
        console.error('Error generating QR code:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tạo QR code',
            error: error.message
        });
    }
});
exports.generateUserQRCode = generateUserQRCode;
// Check-in bằng QR code (admin)
const checkInWithQRCode = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { qrToken } = req.body;
        if (!qrToken) {
            return res.status(400).json({
                success: false,
                message: 'Thiếu QR code token'
            });
        }
        // Xác thực QR code
        const qrData = (0, qrCodeService_1.verifyCheckInQRCode)(qrToken);
        if (!qrData) {
            return res.status(400).json({
                success: false,
                message: 'QR code không hợp lệ hoặc đã hết hạn'
            });
        }
        // Tìm đăng ký
        const registration = yield EventRegistration_1.default.findById(qrData.registrationId)
            .populate('eventId', 'title eventDate location')
            .populate('userId', 'name email');
        if (!registration) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy đăng ký'
            });
        }
        if (registration.status === 'attended') {
            return res.status(400).json({
                success: false,
                message: 'Người dùng đã check-in trước đó',
                registration
            });
        }
        // Cập nhật trạng thái check-in
        registration.status = 'attended';
        registration.checkInTime = new Date();
        yield registration.save();
        res.json({
            success: true,
            message: 'Check-in thành công',
            registration: {
                id: registration._id,
                user: registration.userId,
                event: registration.eventId,
                status: registration.status,
                checkInTime: registration.checkInTime,
                registrationDate: registration.registrationDate
            }
        });
    }
    catch (error) {
        console.error('Error checking in with QR code:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi check-in',
            error: error.message
        });
    }
});
exports.checkInWithQRCode = checkInWithQRCode;
// Tạo QR code chia sẻ sự kiện
const generateShareQRCode = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        // Kiểm tra sự kiện tồn tại
        const event = yield Event_1.default.findById(id);
        if (!event) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy sự kiện'
            });
        }
        const qrCode = yield (0, qrCodeService_1.generateEventShareQRCode)(id);
        res.json({
            success: true,
            qrCode,
            shareUrl: `${process.env.FRONTEND_URL}/events/${id}`
        });
    }
    catch (error) {
        console.error('Error generating share QR code:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tạo QR code chia sẻ',
            error: error.message
        });
    }
});
exports.generateShareQRCode = generateShareQRCode;
