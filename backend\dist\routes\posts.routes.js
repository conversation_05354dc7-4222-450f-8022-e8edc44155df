"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const posts_controller_1 = require("../controllers/posts.controller");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const express_validator_1 = require("express-validator");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const router = (0, express_1.Router)();
// Validation middleware
const createPostValidation = [
    (0, express_validator_1.body)('content')
        .trim()
        .isLength({ min: 1, max: 2000 })
        .withMessage('Nội dung bài viết phải từ 1-2000 ký tự'),
    (0, express_validator_1.body)('visibility')
        .optional()
        .isIn(['public', 'private', 'friends'])
        .withMessage('Visibility phải là public, private hoặc friends'),
    (0, express_validator_1.body)('media')
        .optional()
        .isArray()
        .withMessage('Media phải là một mảng'),
    (0, express_validator_1.body)('media.*')
        .optional()
        .custom((value) => {
        // Chấp nhận cả URL và base64 data URI
        if (typeof value === 'string') {
            // Kiểm tra nếu là URL hợp lệ
            try {
                new URL(value);
                return true;
            }
            catch (_a) {
                // Nếu không phải URL, kiểm tra nếu là base64 data URI
                if (value.startsWith('data:image/') && value.includes('base64,')) {
                    return true;
                }
                throw new Error('Media phải là URL hợp lệ hoặc base64 data URI');
            }
        }
        throw new Error('Media phải là string');
    })
];
const getPostsValidation = [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page phải là số nguyên dương'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('Limit phải từ 1-50'),
    (0, express_validator_1.query)('search')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Từ khóa tìm kiếm tối đa 100 ký tự'),
    (0, express_validator_1.query)('hasMedia')
        .optional()
        .isBoolean()
        .withMessage('hasMedia phải là boolean')
];
const reactValidation = [
    (0, express_validator_1.body)('type')
        .optional()
        .custom((value) => {
        // Allow null, empty string, or valid reaction types
        if (value === null || value === '' || ['like', 'love', 'haha', 'wow', 'sad', 'angry'].includes(value)) {
            return true;
        }
        throw new Error('Loại cảm xúc không hợp lệ');
    })
];
const commentValidation = [
    (0, express_validator_1.body)('content')
        .trim()
        .isLength({ min: 1, max: 500 })
        .withMessage('Nội dung bình luận phải từ 1-500 ký tự')
];
// Routes
/**
 * @route GET /posts
 * @desc Lấy danh sách bài viết công khai (newsfeed)
 * @access Private (cần đăng nhập)
 * @query page - Trang hiện tại (default: 1)
 * @query limit - Số bài viết mỗi trang (default: 10, max: 50)
 * @query search - Từ khóa tìm kiếm trong nội dung
 * @query author - ID người đăng
 * @query hasMedia - Chỉ lấy bài có media (true/false)
 */
router.get('/', auth_middleware_1.verifyToken, getPostsValidation, validation_middleware_1.validateRequest, posts_controller_1.getPosts);
/**
 * @route POST /posts
 * @desc Tạo bài viết mới
 * @access Private (cần đăng nhập)
 * @body content - Nội dung bài viết (required)
 * @body media - Mảng URL media (optional)
 * @body visibility - public/private/friends (default: public)
 */
router.post('/', auth_middleware_1.verifyToken, createPostValidation, validation_middleware_1.validateRequest, posts_controller_1.createPost);
/**
 * @route GET /posts/user/:userId
 * @desc Lấy danh sách bài viết của user cụ thể
 * @access Private (cần đăng nhập)
 */
router.get('/user/:userId', auth_middleware_1.verifyToken, posts_controller_1.getPosts // Sử dụng lại getPosts với filter userId
);
/**
 * @route GET /posts/:id
 * @desc Lấy chi tiết bài viết
 * @access Private (cần đăng nhập)
 */
router.get('/:id', auth_middleware_1.verifyToken, posts_controller_1.getPostById);
/**
 * @route DELETE /posts/:id
 * @desc Xóa bài viết (chỉ chủ sở hữu hoặc admin)
 * @access Private (cần đăng nhập)
 */
router.delete('/:id', auth_middleware_1.verifyToken, posts_controller_1.deletePost);
/**
 * @route POST /posts/:id/react
 * @desc Thả cảm xúc cho bài viết
 * @access Private (cần đăng nhập)
 * @body type - Loại cảm xúc (like/love/haha/sad/angry) hoặc null để bỏ cảm xúc
 */
router.post('/:id/react', auth_middleware_1.verifyToken, reactValidation, validation_middleware_1.validateRequest, posts_controller_1.reactToPost);
/**
 * @route POST /posts/:id/comment
 * @desc Bình luận bài viết
 * @access Private (cần đăng nhập)
 * @body content - Nội dung bình luận (required)
 */
router.post('/:id/comment', auth_middleware_1.verifyToken, commentValidation, validation_middleware_1.validateRequest, posts_controller_1.commentOnPost);
/**
 * @route POST /posts/comments/:commentId/react
 * @desc Thả cảm xúc cho comment
 * @access Private (cần đăng nhập)
 * @body type - Loại cảm xúc (like/love/haha/wow/sad/angry) hoặc null để bỏ cảm xúc
 */
router.post('/comments/:commentId/react', auth_middleware_1.verifyToken, reactValidation, validation_middleware_1.validateRequest, posts_controller_1.reactToComment);
/**
 * @route POST /posts/comments/:commentId/reply
 * @desc Reply comment
 * @access Private (cần đăng nhập)
 * @body content - Nội dung reply (required)
 */
router.post('/comments/:commentId/reply', auth_middleware_1.verifyToken, commentValidation, validation_middleware_1.validateRequest, posts_controller_1.replyToComment);
/**
 * @route POST /posts/:id/report
 * @desc Báo cáo bài viết
 * @access Private (cần đăng nhập)
 * @body reason - Lý do báo cáo (required)
 */
router.post('/:id/report', auth_middleware_1.verifyToken, (0, express_validator_1.body)('reason').notEmpty().withMessage('Lý do báo cáo là bắt buộc'), validation_middleware_1.validateRequest, posts_controller_1.reportPost);
/**
 * @route DELETE /posts/comments/:commentId
 * @desc Xóa comment của chính mình
 * @access Private (cần đăng nhập)
 */
router.delete('/comments/:commentId', auth_middleware_1.verifyToken, posts_controller_1.deleteComment);
/**
 * @route POST /posts/comments/:commentId/report
 * @desc Báo cáo comment
 * @access Private (cần đăng nhập)
 * @body reason - Lý do báo cáo (required)
 */
router.post('/comments/:commentId/report', auth_middleware_1.verifyToken, (0, express_validator_1.body)('reason').notEmpty().withMessage('Lý do báo cáo là bắt buộc'), validation_middleware_1.validateRequest, posts_controller_1.reportComment);
exports.default = router;
