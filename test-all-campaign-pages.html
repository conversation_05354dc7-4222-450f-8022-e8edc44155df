<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test All Campaign Pages</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .page-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .page-link {
            display: block;
            padding: 15px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            transition: background 0.3s;
        }
        .page-link:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        .issue {
            color: #dc3545;
        }
        .issue:before {
            content: "❌ ";
        }
        .fix {
            color: #007bff;
        }
        .fix:before {
            content: "🔧 ";
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test All Campaign Pages</h1>
        <p>Kiểm tra tất cả các trang hiển thị campaigns sau khi fix hình ảnh</p>

        <!-- Frontend Pages -->
        <div class="test-section">
            <h3>📱 Frontend Pages</h3>
            <div class="page-links">
                <a href="http://localhost:5173/campaigns" class="page-link" target="_blank">
                    📋 Danh sách Campaigns
                </a>
                <a href="http://localhost:5173/campaigns/[campaign-id]" class="page-link" target="_blank">
                    📄 Chi tiết Campaign
                </a>
                <a href="http://localhost:5173/" class="page-link" target="_blank">
                    🏠 Homepage (Featured Campaigns)
                </a>
            </div>
        </div>

        <!-- Admin Pages -->
        <div class="test-section">
            <h3>⚙️ Admin Pages</h3>
            <div class="page-links">
                <a href="http://localhost:5173/admin/campaigns" class="page-link" target="_blank">
                    📊 Quản lý Campaigns
                </a>
                <a href="http://localhost:5173/admin/campaigns/create" class="page-link" target="_blank">
                    ➕ Tạo Campaign mới
                </a>
            </div>
        </div>

        <!-- What Was Fixed -->
        <div class="test-section">
            <h3>🔧 Những gì đã được sửa</h3>
            <ul class="checklist">
                <li>Thay thế tất cả <code>img</code> tags bằng <code>SafeImage</code> component</li>
                <li>Xử lý data URLs (base64) đúng cách</li>
                <li>Xử lý blob URLs đúng cách</li>
                <li>Loại bỏ function <code>getImageUrl</code> cũ</li>
                <li>Cập nhật Campaigns.tsx - trang danh sách</li>
                <li>Cập nhật CampaignDetail.tsx - trang chi tiết</li>
                <li>Cập nhật ManageCampaigns.tsx - admin</li>
                <li>Cập nhật CampaignModal.tsx - form tạo/sửa</li>
            </ul>
        </div>

        <!-- Test Checklist -->
        <div class="test-section">
            <h3>✅ Checklist kiểm tra</h3>
            <ul class="checklist">
                <li>Hình ảnh hiển thị đúng trong danh sách campaigns</li>
                <li>Hình ảnh hiển thị đúng trong chi tiết campaign</li>
                <li>Thumbnail gallery hoạt động bình thường</li>
                <li>Upload hình ảnh mới hoạt động</li>
                <li>Preview hình ảnh khi upload</li>
                <li>Thay thế hình ảnh hoạt động</li>
                <li>Xóa hình ảnh hoạt động</li>
                <li>Không còn console errors</li>
                <li>Placeholder hiển thị khi hình ảnh lỗi</li>
                <li>Admin table hiển thị thumbnail đúng</li>
            </ul>
        </div>

        <!-- Common Issues -->
        <div class="test-section">
            <h3>🚨 Các vấn đề thường gặp</h3>
            <ul class="checklist">
                <li class="issue">Hình ảnh hiển thị "Hình ảnh không tải được"</li>
                <li class="fix">Kiểm tra backend có chạy port 5001 không</li>
                <li class="fix">Kiểm tra file có tồn tại trong backend/uploads/</li>
                <li class="fix">Chạy script fix database: ./emergency-fix.bat</li>
                
                <li class="issue">Console vẫn có errors</li>
                <li class="fix">Clear browser cache (Ctrl+Shift+R)</li>
                <li class="fix">Restart frontend: npm run dev</li>
                
                <li class="issue">Upload không hoạt động</li>
                <li class="fix">Kiểm tra kích thước file (<10MB)</li>
                <li class="fix">Kiểm tra định dạng file (JPG, PNG, GIF)</li>
                <li class="fix">Kiểm tra số lượng file (<5 ảnh)</li>
            </ul>
        </div>

        <!-- Status Check -->
        <div class="test-section">
            <h3>📊 Kiểm tra trạng thái</h3>
            <div id="statusCheck">
                <button onclick="checkBackend()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px;">
                    Check Backend
                </button>
                <button onclick="checkFrontend()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px;">
                    Check Frontend
                </button>
                <div id="statusResults"></div>
            </div>
        </div>
    </div>

    <script>
        async function checkBackend() {
            const resultsDiv = document.getElementById('statusResults');
            resultsDiv.innerHTML = '<div class="status info">Checking backend...</div>';
            
            try {
                const response = await fetch('http://localhost:5001/api/campaigns');
                if (response.ok) {
                    resultsDiv.innerHTML = '<div class="status success">✅ Backend is running on port 5001</div>';
                } else {
                    resultsDiv.innerHTML = '<div class="status error">❌ Backend responded with error: ' + response.status + '</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML = '<div class="status error">❌ Backend is not running or not accessible</div>';
            }
        }
        
        async function checkFrontend() {
            const resultsDiv = document.getElementById('statusResults');
            resultsDiv.innerHTML = '<div class="status info">Checking frontend...</div>';
            
            try {
                const response = await fetch('http://localhost:5173/');
                if (response.ok) {
                    resultsDiv.innerHTML = '<div class="status success">✅ Frontend is running on port 5173</div>';
                } else {
                    resultsDiv.innerHTML = '<div class="status error">❌ Frontend responded with error: ' + response.status + '</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML = '<div class="status error">❌ Frontend is not running or not accessible</div>';
            }
        }
        
        // Auto-check on page load
        window.onload = () => {
            setTimeout(checkBackend, 1000);
        };
    </script>
</body>
</html>
