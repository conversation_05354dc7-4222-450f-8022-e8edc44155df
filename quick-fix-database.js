// Quick fix for database images - no dependencies needed
const https = require('https');
const http = require('http');

// Simple MongoDB connection without mongoose
const { MongoClient } = require('mongodb');

// Get MongoDB URI from environment or use default
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://keydyweb:<EMAIL>/keydyweb?retryWrites=true&w=majority';

async function quickFixDatabase() {
  let client;
  
  try {
    console.log('🔌 Connecting to MongoDB...');
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    console.log('✅ Connected to MongoDB');

    const db = client.db();
    const campaigns = db.collection('campaigns');

    // Get all campaigns
    const allCampaigns = await campaigns.find({}).toArray();
    console.log(`📊 Found ${allCampaigns.length} campaigns`);

    let fixedCount = 0;

    for (const campaign of allCampaigns) {
      let hasChanges = false;
      const newImages = [];

      console.log(`\n🔍 Checking campaign: ${campaign.title}`);
      
      if (campaign.images && Array.isArray(campaign.images)) {
        console.log(`📁 Current images: ${campaign.images.length}`);

        for (const imagePath of campaign.images) {
          console.log(`  - Checking: ${imagePath}`);
          
          // If image path contains UUID (long filename), fix it
          if (imagePath && imagePath.includes('-') && imagePath.split('-').length > 2) {
            // Extract timestamp part only
            const parts = imagePath.split('/');
            const filename = parts[parts.length - 1];
            const filenameParts = filename.split('-');
            
            if (filenameParts.length >= 2) {
              const simpleFilename = `${filenameParts[0]}-${filenameParts[1]}.jpg`;
              const newPath = `/uploads/${simpleFilename}`;
              newImages.push(newPath);
              console.log(`    🔄 Fixed: ${imagePath} -> ${newPath}`);
              hasChanges = true;
            } else {
              newImages.push(imagePath);
              console.log(`    ✅ Kept: ${imagePath}`);
            }
          } else {
            newImages.push(imagePath);
            console.log(`    ✅ Kept: ${imagePath}`);
          }
        }

        if (hasChanges) {
          // Update campaign
          await campaigns.updateOne(
            { _id: campaign._id },
            {
              $set: {
                images: newImages,
                image: newImages.length > 0 ? newImages[0] : ''
              }
            }
          );
          
          fixedCount++;
          console.log(`  ✅ Campaign fixed: ${campaign.title}`);
          console.log(`  📁 Old images: ${campaign.images.length}`);
          console.log(`  📁 New images: ${newImages.length}`);
        } else {
          console.log(`  ✅ Campaign OK: ${campaign.title}`);
        }
      } else {
        console.log(`  ℹ️ No images array found for: ${campaign.title}`);
      }
    }

    console.log(`\n🎉 Summary:`);
    console.log(`  - Total campaigns: ${allCampaigns.length}`);
    console.log(`  - Fixed campaigns: ${fixedCount}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Full error:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 Disconnected from MongoDB');
    }
  }
}

// Check if mongodb driver is available
try {
  require('mongodb');
  quickFixDatabase();
} catch (error) {
  console.log('❌ MongoDB driver not found. Installing...');
  console.log('Please run: npm install mongodb');
  console.log('Then run this script again.');
}
