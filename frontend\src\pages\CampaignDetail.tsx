import { useEffect, useState, useRef } from "react";
import { useParams, useLocation } from "react-router-dom";
import axios from "axios";
import { toast } from "sonner";
import { motion, AnimatePresence } from 'framer-motion';
import {
  Heart,
  Target,
  Users,
  Calendar,
  TrendingUp,
  MessageCircle,
  Share2,
  ChevronLeft,
  ChevronRight,
  X,
  User,
  EyeOff,
  Tag,
  CheckCircle,
  XCircle
} from 'lucide-react';
import SafeImage from '../components/ui/SafeImage';
import './CampaignStyles.css';

type Campaign = {
  _id: string;
  title: string;
  description: string;
  targetAmount: number;
  currentAmount: number;
  images: string[];
  startDate: string;
  endDate: string;
  status: 'active' | 'completed' | 'cancelled';
  organizationName?: string;
  donorsCount?: number;
  totalDonors?: number;
  totalDonations?: number;
  progress?: number;
  organizationLogo?: string;
  category?: string;
};

type Donor = {
  _id: string;
  name: string;
  amount: number;
  message?: string;
  createdAt: string;
  isAnonymous: boolean;
};

// Constants
const API_URL = import.meta.env.VITE_API_URL || '';

// Note: getImageUrl function removed - now using SafeImage component

const CampaignDetail = () => {
  const { id: campaignId } = useParams();
  const location = useLocation();
  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [donors, setDonors] = useState<Donor[]>([]);
  const [mainImageIndex, setMainImageIndex] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<'content' | 'donors'>('content');
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [showDonationForm, setShowDonationForm] = useState<boolean>(false);
  const [formData, setFormData] = useState({
    fullName: '',
    phone: '',
    address: '',
    email: '',
    isAnonymous: false,
    supportAmount: '20000',
    paymentMethod: 'MOMO',
    message: ''
  });
  const [loading, setLoading] = useState<boolean>(true);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const [lastAmount, setLastAmount] = useState<number>(0);
  const [lastDonorCount, setLastDonorCount] = useState<number>(0);

  const fetchCampaignAndDonors = async () => {
    try {
      // Fetch campaign
      const campaignRes = await axios.get(`${API_URL}/api/campaigns/${campaignId}`);
      const campaignData = campaignRes.data;
      // Fetch donors
      const donorsRes = await axios.get(`${API_URL}/api/donations/campaign/${campaignId}`);
      setCampaign(campaignData);
      setDonors(donorsRes.data);
      setLoading(false);

      // Kiểm tra nếu số tiền hoặc số lượng người ủng hộ đã thay đổi
      if (
        campaignData.currentAmount > lastAmount ||
        donorsRes.data.length > lastDonorCount
      ) {
        setLastAmount(campaignData.currentAmount);
        setLastDonorCount(donorsRes.data.length);
        if (pollingRef.current) {
          clearInterval(pollingRef.current);
          pollingRef.current = null;
          toast.success("Cập nhật thành công!");
        }
      }
      return { campaignData, donorsCount: donorsRes.data.length };
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Không thể tải thông tin cập nhật");
      setLoading(false);
      return null;
    }
  };

  // Initial fetch
  useEffect(() => {
    if (campaignId) {
      fetchCampaignAndDonors();
    }
  }, [campaignId]);

  // Handle payment success notification
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const status = urlParams.get('status');
    const transactionId = urlParams.get('transactionId');

    if (status === 'success' && transactionId) {
      toast.success(`Cảm ơn bạn đã ủng hộ! Mã giao dịch: ${transactionId}`);
    } else if (status === 'failed' && transactionId) {
      toast.error(`Thanh toán thất bại. Mã giao dịch: ${transactionId}`);
    }
  }, [location.search]);

  // Polling sau khi thanh toán thành công
  useEffect(() => {
    if (location.search.includes('status=success')) {
      let attempts = 0;
      const maxAttempts = 10; // Tối đa 10 lần thử (30 giây)
      // Lưu số tiền và số lượng người ủng hộ hiện tại để so sánh
      if (campaign) {
        setLastAmount(campaign.currentAmount);
      }
      setLastDonorCount(donors.length);
      // Bắt đầu polling
      pollingRef.current = setInterval(async () => {
        const updated = await fetchCampaignAndDonors();
        attempts++;
        // Dừng polling nếu:
        // 1. Đã thử đủ số lần
        // 2. Số tiền đã thay đổi
        // 3. Số lượng người ủng hộ đã thay đổi
        // 4. Có lỗi khi fetch
        if (
          attempts >= maxAttempts ||
          (updated && (updated.campaignData.currentAmount > lastAmount || updated.donorsCount > lastDonorCount)) ||
          !updated
        ) {
          if (pollingRef.current) {
            clearInterval(pollingRef.current);
            pollingRef.current = null;
          }
        }
      }, 3000); // Poll mỗi 3 giây
      return () => {
        if (pollingRef.current) {
          clearInterval(pollingRef.current);
          pollingRef.current = null;
        }
      };
    }
  }, [location.search, campaignId]);

  // Cleanup intervals
  useEffect(() => {
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
      if (pollingRef.current) clearInterval(pollingRef.current);
    };
  }, []);

  // Auto image scroll (optional - keep if desired)
  useEffect(() => {
    if (campaign && campaign.images.length > 1 && !isPaused) {
      intervalRef.current = setInterval(() => {
        setMainImageIndex((prev) => (prev + 1) % campaign.images.length);
      }, 5000);
    }
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [campaign, isPaused]);

  const handleImageClick = (index: number) => {
    setMainImageIndex(index);
  };

  const handlePrev = () => {
    if (!campaign) return;
    setMainImageIndex((prev) =>
      (prev - 1 + campaign.images.length) % campaign.images.length
    );
  };

  const handleNext = () => {
    if (!campaign) return;
    setMainImageIndex((prev) => (prev + 1) % campaign.images.length);
  };

  // Keyboard controls (optional - keep if desired)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") handlePrev();
      else if (e.key === "ArrowRight") handleNext();
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [campaign, handlePrev, handleNext]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.fullName.trim()) {
      toast.error("Vui lòng nhập họ tên");
      return;
    }
    if (!formData.email.trim()) {
      toast.error("Vui lòng nhập email");
      return;
    }

    // Validate amount
    const amount = parseInt(formData.supportAmount);
    if (isNaN(amount) || amount < 10000) {
      toast.error("Số tiền ủng hộ tối thiểu là 10,000 VNĐ");
      return;
    }

    try {
      setLoading(true);
      const response = await axios.post(`${API_URL}/api/donations`, {
        campaignId: campaignId,
        amount: amount,
        paymentMethod: formData.paymentMethod,
        name: formData.fullName,
        email: formData.email,
        phone: formData.phone || undefined,
        address: formData.address || undefined,
        isAnonymous: formData.isAnonymous,
        message: formData.message || undefined
      });

      if (response.data.paymentUrl) {
        if (formData.paymentMethod === 'PAYOS') {
          window.location.href = response.data.paymentUrl;
        } else {
          // Xử lý cho MoMo hoặc các phương thức khác nếu cần
          window.location.href = response.data.paymentUrl;
        }
      } else {
        toast.error("Không thể tạo URL thanh toán. Vui lòng thử lại sau.");
      }
    } catch (error: any) {
      console.error("Error creating donation:", error);
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Có lỗi xảy ra khi tạo đơn ủng hộ. Vui lòng thử lại sau.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!campaign) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-purple-100 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">Đang tải thông tin chiến dịch...</p>
        </motion.div>
      </div>
    );
  }

  // Use progress from backend if available, otherwise calculate
  const percentRaised = campaign?.progress
    ? campaign.progress.toFixed(1)
    : Math.min((campaign?.currentAmount || 0) / (campaign?.targetAmount && campaign.targetAmount > 0 ? campaign.targetAmount : 1) * 100, 100).toFixed(1);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-purple-100 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full opacity-20 blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl mb-4 shadow-xl"
          >
            <Heart className="w-6 h-6 text-white" />
          </motion.div>
          <h1 className="campaign-heading text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800 mb-3 leading-tight">
            {campaign.title}
          </h1>

          {/* Category and Status */}
          <div className="flex flex-wrap justify-center gap-3 mb-4">
            {campaign.category && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="category-badge inline-flex items-center px-4 py-2 rounded-full text-sm bg-purple-600 text-white shadow-lg"
              >
                <Tag className="w-4 h-4 mr-2" />
                <span>{campaign.category}</span>
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold text-white shadow-lg ${
                campaign.status === 'active'
                  ? 'bg-gradient-to-r from-green-500 to-emerald-500'
                  : campaign.status === 'completed'
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-500'
                  : 'bg-gradient-to-r from-red-500 to-pink-500'
              }`}
            >
              {campaign.status === 'active' && (
                <>
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Đang gây quỹ
                </>
              )}
              {campaign.status === 'completed' && (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Đã hoàn thành
                </>
              )}
              {campaign.status === 'cancelled' && (
                <>
                  <XCircle className="w-4 h-4 mr-2" />
                  Đã hủy
                </>
              )}
            </motion.div>
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left: Image Gallery */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="lg:col-span-2"
          >
            <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow-xl border border-white/20 overflow-hidden">
              <div
                className="relative group"
                onMouseEnter={() => setIsPaused(true)}
                onMouseLeave={() => setIsPaused(false)}
              >
                <div className="relative h-96 overflow-hidden">
                  {campaign.images.length > 0 ? (
                    <motion.div
                      key={mainImageIndex}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.5 }}
                      className="w-full h-full"
                    >
                      <SafeImage
                        src={campaign.images[mainImageIndex]}
                        alt="Chiến dịch"
                        className="w-full h-full object-cover"
                      />
                    </motion.div>
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-purple-200 to-pink-200 flex items-center justify-center">
                      <Heart className="w-20 h-20 text-purple-400" />
                    </div>
                  )}

                  {/* Navigation Buttons */}
                  {campaign.images.length > 1 && (
                    <>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={handlePrev}
                        className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white text-gray-800 p-3 rounded-full shadow-lg transition-all duration-300 opacity-0 group-hover:opacity-100"
                      >
                        <ChevronLeft className="w-5 h-5" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={handleNext}
                        className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white text-gray-800 p-3 rounded-full shadow-lg transition-all duration-300 opacity-0 group-hover:opacity-100"
                      >
                        <ChevronRight className="w-5 h-5" />
                      </motion.button>
                    </>
                  )}

                  {/* Image Counter */}
                  {campaign.images.length > 1 && (
                    <div className="absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                      {mainImageIndex + 1} / {campaign.images.length}
                    </div>
                  )}
                </div>

                {/* Thumbnails */}
                {campaign.images.length > 1 && (
                  <div className="p-4 bg-gray-50/50">
                    <div className="flex gap-3 overflow-x-auto pb-2">
                      {campaign.images.map((img, index) => (
                        <motion.div
                          key={index}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => handleImageClick(index)}
                          className={`w-20 h-20 rounded-xl cursor-pointer border-2 shadow-sm transition-all duration-300 flex-shrink-0 overflow-hidden ${
                            index === mainImageIndex
                              ? "border-purple-500 ring-2 ring-purple-200"
                              : "border-gray-200 hover:border-purple-300"
                          }`}
                        >
                          <SafeImage
                            src={img}
                            alt={`Thumbnail ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Right: Campaign Info */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="lg:col-span-1"
          >
            <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow-xl border border-white/20 p-6 sticky top-8">
              {/* Organization Info */}
              <div className="flex items-center space-x-3 mb-6">
                {campaign?.organizationLogo ? (
                  <img
                    src={campaign.organizationLogo}
                    alt="Organization Logo"
                    className="w-12 h-12 rounded-full object-cover border-2 border-purple-200"
                  />
                ) : (
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <Heart className="w-6 h-6 text-white" />
                  </div>
                )}
                <div className="flex-1">
                  <p className="campaign-text text-purple-600 font-semibold">{campaign?.organizationName || 'Tổ chức quyên góp'}</p>
                  <div className="campaign-text flex items-center text-gray-500 text-sm space-x-1">
                    <Users className="w-4 h-4" />
                    <span>{campaign?.totalDonors || donors.length || 0} lượt ủng hộ</span>
                  </div>
                </div>
              </div>

              {/* Progress Section */}
              <div className="space-y-4">
                {/* Target Amount */}
                <div className="campaign-text flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Target className="w-5 h-5 text-purple-500" />
                    <span className="text-gray-600">Mục tiêu</span>
                  </div>
                  <span className="font-bold text-gray-800">
                    {campaign?.targetAmount?.toLocaleString() || '0'} VNĐ
                  </span>
                </div>

                {/* Progress Bar */}
                <div className="relative">
                  <div className="w-full h-4 rounded-full bg-gray-200 overflow-hidden">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${percentRaised}%` }}
                      transition={{ duration: 1.5, delay: 0.5 }}
                      className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full relative"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
                    </motion.div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xs font-bold text-white drop-shadow-lg">
                      {percentRaised}%
                    </span>
                  </div>
                </div>

                {/* Current Amount */}
                <div className="campaign-text flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-5 h-5 text-green-500" />
                    <span className="text-gray-600">Đã đạt được</span>
                  </div>
                  <span className="font-bold text-2xl bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    {campaign?.currentAmount?.toLocaleString() || '0'} VNĐ
                  </span>
                </div>

                {/* Campaign Dates */}
                <div className="pt-4 border-t border-gray-200">
                  <div className="campaign-text flex items-center space-x-2 text-gray-600 text-sm">
                    <Calendar className="w-4 h-4" />
                    <span>Thời gian: {new Date(campaign.startDate).toLocaleDateString('vi-VN')} - {new Date(campaign.endDate).toLocaleDateString('vi-VN')}</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mt-8 space-y-3">
                {campaign.status === 'active' ? (
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="button"
                    onClick={() => setShowDonationForm(true)}
                    className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold rounded-xl px-6 py-4 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center space-x-2"
                  >
                    <Heart className="w-5 h-5" />
                    <span>Ủng hộ ngay</span>
                  </motion.button>
                ) : (
                  <div className="w-full bg-gray-300 text-gray-500 font-semibold rounded-xl px-6 py-4 flex items-center justify-center space-x-2 cursor-not-allowed">
                    {campaign.status === 'completed' ? (
                      <>
                        <CheckCircle className="w-5 h-5" />
                        <span>Chiến dịch đã kết thúc</span>
                      </>
                    ) : (
                      <>
                        <XCircle className="w-5 h-5" />
                        <span>Chiến dịch đã hủy</span>
                      </>
                    )}
                  </div>
                )}

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  className="w-full bg-white/50 hover:bg-white/80 text-gray-700 font-medium rounded-xl px-6 py-3 transition-all duration-300 border border-gray-200 hover:border-gray-300 flex items-center justify-center space-x-2"
                >
                  <Share2 className="w-4 h-4" />
                  <span>Chia sẻ</span>
                </motion.button>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Content Tabs */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-16"
        >
          <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow-xl border border-white/20 overflow-hidden">
            {/* Tab Headers */}
            <div className="flex border-b border-gray-200/50">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="button"
                onClick={() => setActiveTab('content')}
                className={`flex-1 px-6 py-4 font-semibold transition-all duration-300 ${
                  activeTab === 'content'
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50/50'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <MessageCircle className="w-5 h-5" />
                  <span>Nội dung chiến dịch</span>
                </div>
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="button"
                onClick={() => setActiveTab('donors')}
                className={`flex-1 px-6 py-4 font-semibold transition-all duration-300 ${
                  activeTab === 'donors'
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50/50'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <Users className="w-5 h-5" />
                  <span>Danh sách ủng hộ ({donors.length})</span>
                </div>
              </motion.button>
            </div>

            {/* Tab Content */}
            <div className="p-8">
              <AnimatePresence mode="wait">
                {activeTab === 'content' ? (
                  <motion.div
                    key="content"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="prose prose-gray max-w-none">
                      <p className="campaign-text text-gray-700 leading-relaxed text-lg whitespace-pre-wrap">
                        {campaign?.description}
                      </p>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="donors"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    {donors.length > 0 ? (
                      <div className="space-y-4">
                        {donors.map((donor, index) => (
                          <motion.div
                            key={donor._id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.1 }}
                            className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-100 hover:shadow-lg transition-all duration-300"
                          >
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="flex items-center space-x-3 mb-2">
                                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                    {donor.isAnonymous ? (
                                      <EyeOff className="w-5 h-5 text-white" />
                                    ) : (
                                      <User className="w-5 h-5 text-white" />
                                    )}
                                  </div>
                                  <div>
                                    <h4 className="campaign-text font-semibold text-gray-800">
                                      {donor.isAnonymous ? 'Người ủng hộ ẩn danh' : donor.name}
                                    </h4>
                                    <p className="campaign-text text-gray-500 text-sm">
                                      {new Date(donor.createdAt).toLocaleDateString('vi-VN', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                      })}
                                    </p>
                                  </div>
                                </div>
                                {donor.message && (
                                  <div className="bg-white/50 rounded-lg p-3 mt-3">
                                    <p className="campaign-text text-gray-700 italic">"{donor.message}"</p>
                                  </div>
                                )}
                              </div>
                              <div className="text-right ml-4">
                                <p className="campaign-text text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                                  {donor.amount.toLocaleString()} VNĐ
                                </p>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <div className="w-20 h-20 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <Users className="w-10 h-10 text-purple-400" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-700 mb-2">Chưa có lượt ủng hộ nào</h3>
                        <p className="text-gray-500">Hãy là người đầu tiên ủng hộ chiến dịch này!</p>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </motion.section>
      </div>

      {/* Donation Modal */}
      <AnimatePresence>
        {showDonationForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            onClick={() => setShowDonationForm(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ duration: 0.3 }}
              className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 p-8 max-w-md w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-600 rounded-xl flex items-center justify-center">
                    <Heart className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="campaign-heading text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Thông tin ủng hộ
                  </h3>
                </div>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100 transition-all duration-200"
                  onClick={() => setShowDonationForm(false)}
                  aria-label="Đóng form"
                >
                  <X className="w-6 h-6" />
                </motion.button>
              </div>

            {/* Amount Input */}
            <div className="mb-4 p-4 bg-gray-50 rounded-lg">
              <label htmlFor="supportAmount" className="block text-sm text-gray-600">Số tiền ủng hộ:</label>

              {/* Preset amounts */}
              <div className="grid grid-cols-3 gap-2 mt-2 mb-3">
                {[10000, 20000, 50000, 100000, 200000, 500000].map(amount => (
                  <button
                    key={amount}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, supportAmount: amount.toString() }))}
                    className={`px-3 py-2 text-sm rounded-md border transition-colors ${
                      formData.supportAmount === amount.toString()
                        ? 'bg-pink-600 text-white border-pink-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {amount.toLocaleString()}
                  </button>
                ))}
              </div>

              <div className="relative">
                <input
                  type="text"
                  id="supportAmount"
                  name="supportAmount"
                  value={formData.supportAmount}
                  onChange={(e) => {
                    const value = e.target.value.replace(/[^0-9]/g, '');
                    setFormData(prev => ({ ...prev, supportAmount: value }));
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-600"
                  placeholder="Hoặc nhập số tiền khác"
                  required
                  aria-label="Số tiền ủng hộ"
                />
                <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 text-sm">VNĐ</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">Số tiền tối thiểu: 10,000 VNĐ</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-700">
                  Họ và tên <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="fullName"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleChange}
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  autoComplete="name"
                  aria-label="Họ và tên"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  autoComplete="email"
                  aria-label="Email"
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                  Số điện thoại
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  autoComplete="tel"
                  aria-label="Số điện thoại"
                />
              </div>

              <div>
                <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                  Địa chỉ
                </label>
                <input
                  type="text"
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  autoComplete="street-address"
                  aria-label="Địa chỉ"
                />
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700">
                  Lời nhắn
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  rows={3}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  aria-label="Lời nhắn"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isAnonymous"
                  name="isAnonymous"
                  checked={formData.isAnonymous}
                  onChange={handleChange}
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  aria-label="Ẩn danh"
                />
                <label htmlFor="isAnonymous" className="ml-2 block text-sm text-gray-700">
                  Ẩn danh
                </label>
              </div>

              <div>
                <fieldset>
                  <legend className="block text-sm font-medium text-gray-700 mb-2">
                    Phương thức thanh toán
                  </legend>
                  <div className="grid grid-cols-2 gap-4">
                    <label className="relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="MOMO"
                        checked={formData.paymentMethod === 'MOMO'}
                        onChange={handleChange}
                        className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300"
                        aria-label="Thanh toán qua MoMo"
                      />
                      <div className="ml-3">
                        <span className="block text-sm font-medium text-gray-900">MoMo</span>
                        <span className="block text-xs text-gray-500">Thanh toán qua MoMo</span>
                      </div>
                    </label>
                    <label className="relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="PAYOS"
                        checked={formData.paymentMethod === 'PAYOS'}
                        onChange={handleChange}
                        className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300"
                        aria-label="Thanh toán qua PayOS"
                      />
                      <div className="ml-3">
                        <span className="block text-sm font-medium text-gray-900">PayOS</span>
                        <span className="block text-xs text-gray-500">Thanh toán qua PayOS</span>
                      </div>
                    </label>
                  </div>
                </fieldset>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full rounded-md bg-pink-600 px-4 py-2 text-white hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50"
                aria-label="Quyên góp ngay"
              >
                {loading ? 'Đang xử lý...' : 'Quyên góp ngay'}
              </button>
            </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CampaignDetail;
