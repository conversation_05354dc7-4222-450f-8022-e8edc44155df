import { Request, Response } from 'express';
export declare const getPosts: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const createPost: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getPostById: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const reactToPost: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const commentOnPost: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const reactToComment: (req: any, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const replyToComment: (req: any, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteComment: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deletePost: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const reportPost: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const reportComment: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=posts.controller.d.ts.map