@echo off
echo ========================================
echo    FIX DATABASE IMAGES SCRIPT
echo ========================================
echo.
echo This script will fix image paths in database
echo by removing UUID from filenames.
echo.
echo Before running, make sure:
echo 1. MongoDB is accessible
echo 2. Backend .env file exists
echo 3. You have backup of database
echo.
pause
echo.
echo Installing required dependencies...
npm install mongoose dotenv
echo.
echo Running database fix script...
node fix-database-images.js
echo.
echo ========================================
echo    SCRIPT COMPLETED
echo ========================================
pause
