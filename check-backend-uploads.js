const fs = require('fs');
const path = require('path');
const http = require('http');

async function checkBackendUploads() {
  console.log('🔍 CHECKING BACKEND UPLOADS...\n');

  // 1. Check if backend uploads directory exists
  const uploadsDir = path.join(__dirname, 'backend/uploads');
  console.log(`📁 Checking uploads directory: ${uploadsDir}`);
  
  if (!fs.existsSync(uploadsDir)) {
    console.log('❌ Backend uploads directory does not exist!');
    console.log('🔧 Creating uploads directory...');
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log('✅ Created uploads directory');
  } else {
    console.log('✅ Uploads directory exists');
  }

  // 2. List files in uploads directory
  const files = fs.readdirSync(uploadsDir);
  const imageFiles = files.filter(file =>
    file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.gif') || 
    file.endsWith('.jpeg') || file.endsWith('.svg')
  );

  console.log(`\n📊 Files in uploads directory:`);
  console.log(`   Total files: ${files.length}`);
  console.log(`   Image files: ${imageFiles.length}`);

  if (imageFiles.length === 0) {
    console.log('⚠️ No image files found!');
    console.log('🎨 Creating sample images...');
    
    // Create a simple test image
    const testImageSvg = `
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <rect width="400" height="300" fill="#4ECDC4"/>
        <text x="200" y="150" text-anchor="middle" font-family="Arial" font-size="24" fill="white">
          Test Image
        </text>
        <text x="200" y="180" text-anchor="middle" font-family="Arial" font-size="16" fill="white">
          ${new Date().toISOString()}
        </text>
      </svg>
    `;
    
    const testImagePath = path.join(uploadsDir, `test-image-${Date.now()}.svg`);
    fs.writeFileSync(testImagePath, testImageSvg);
    console.log(`✅ Created test image: ${path.basename(testImagePath)}`);
  } else {
    console.log('\n📷 Image files found:');
    imageFiles.forEach((file, index) => {
      const filePath = path.join(uploadsDir, file);
      const stats = fs.statSync(filePath);
      console.log(`   ${index + 1}. ${file} (${(stats.size / 1024).toFixed(1)}KB)`);
    });
  }

  // 3. Check if backend server is running
  console.log('\n🌐 Checking backend server...');
  
  try {
    const isRunning = await checkServerRunning('localhost', 5001);
    if (isRunning) {
      console.log('✅ Backend server is running on port 5001');
      
      // Test image serving
      if (imageFiles.length > 0) {
        const testFile = imageFiles[0];
        const testUrl = `http://localhost:5001/uploads/${testFile}`;
        console.log(`\n🧪 Testing image serving: ${testUrl}`);
        
        try {
          const accessible = await checkUrlAccessible(testUrl);
          if (accessible) {
            console.log('✅ Images are being served correctly');
          } else {
            console.log('❌ Images are not accessible via HTTP');
            console.log('🔧 Check backend static file serving configuration');
          }
        } catch (error) {
          console.log(`❌ Error testing image URL: ${error.message}`);
        }
      }
    } else {
      console.log('❌ Backend server is not running on port 5001');
      console.log('🔧 Please start backend server:');
      console.log('   cd backend');
      console.log('   npm start');
    }
  } catch (error) {
    console.log(`❌ Error checking backend server: ${error.message}`);
  }

  // 4. Check backend static file configuration
  console.log('\n⚙️ Checking backend configuration...');
  const backendDir = path.join(__dirname, 'backend');
  const serverFiles = ['server.js', 'app.js', 'index.js'];
  
  for (const serverFile of serverFiles) {
    const serverPath = path.join(backendDir, serverFile);
    if (fs.existsSync(serverPath)) {
      console.log(`📄 Found server file: ${serverFile}`);
      
      const content = fs.readFileSync(serverPath, 'utf8');
      if (content.includes('express.static') && content.includes('uploads')) {
        console.log('✅ Static file serving for uploads is configured');
      } else {
        console.log('⚠️ Static file serving for uploads might not be configured');
        console.log('🔧 Make sure you have: app.use("/uploads", express.static("uploads"));');
      }
      break;
    }
  }

  console.log('\n🎯 SUMMARY:');
  console.log(`   - Uploads directory: ${fs.existsSync(uploadsDir) ? '✅' : '❌'}`);
  console.log(`   - Image files: ${imageFiles.length} found`);
  console.log(`   - Backend server: ${await checkServerRunning('localhost', 5001) ? '✅' : '❌'}`);
}

// Helper function to check if server is running
function checkServerRunning(host, port) {
  return new Promise((resolve) => {
    const req = http.request({ host, port, method: 'HEAD', path: '/' }, (res) => {
      resolve(true);
    });
    
    req.on('error', () => {
      resolve(false);
    });
    
    req.setTimeout(2000, () => {
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

// Helper function to check if URL is accessible
function checkUrlAccessible(url) {
  return new Promise((resolve) => {
    const urlObj = new URL(url);
    const req = http.request({
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: 'HEAD'
    }, (res) => {
      resolve(res.statusCode === 200);
    });
    
    req.on('error', () => {
      resolve(false);
    });
    
    req.setTimeout(3000, () => {
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

checkBackendUploads().catch(console.error);
